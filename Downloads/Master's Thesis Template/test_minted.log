This is XeTeX, Version 3.141592653-2.6-0.999993 (MiKTeX 22.1) (preloaded format=xelatex 2024.11.9)  17 APR 2025 16:42
entering extended mode
 \write18 enabled.
 %&-line parsing enabled.
**./test_minted.tex
(test_minted.tex
LaTeX2e <2023-06-01> patch level 1
L3 programming layer <2023-08-29>
(/Library/Application Support/MiKTeX/texmfs/install/tex/latex/base/article.cls
Document Class: article 2023/05/17 v1.4n Standard LaTeX document class
(/Library/Application Support/MiKTeX/texmfs/install/tex/latex/base/size10.clo
File: size10.clo 2023/05/17 v1.4n Standard LaTeX file (size option)
)
\c@part=\count181
\c@section=\count182
\c@subsection=\count183
\c@subsubsection=\count184
\c@paragraph=\count185
\c@subparagraph=\count186
\c@figure=\count187
\c@table=\count188
\abovecaptionskip=\skip48
\belowcaptionskip=\skip49
\bibindent=\dimen140
)
(/Users/<USER>/Library/Application Support/MiKTeX/texmfs/install/tex/late
x/minted/minted.sty
Package: minted 2023/09/12 v2.8 Yet another Pygments shim for LaTeX

(/Library/Application Support/MiKTeX/texmfs/install/tex/latex/graphics/keyval.s
ty
Package: keyval 2022/05/29 v1.15 key=value parser (DPC)
\KV@toks@=\toks17
)
(/Users/<USER>/Library/Application Support/MiKTeX/texmfs/install/tex/late
x/kvoptions/kvoptions.sty
Package: kvoptions 2022-06-15 v3.15 Key value format for package options (HO)

(/Users/<USER>/Library/Application Support/MiKTeX/texmfs/install/tex/gene
ric/ltxcmds/ltxcmds.sty
Package: ltxcmds 2023-12-04 v1.26 LaTeX kernel commands for general use (HO)
)
(/Users/<USER>/Library/Application Support/MiKTeX/texmfs/install/tex/late
x/kvsetkeys/kvsetkeys.sty
Package: kvsetkeys 2022-10-05 v1.19 Key value parser (HO)
))
(/Users/<USER>/Library/Application Support/MiKTeX/texmfs/install/tex/late
x/fvextra/fvextra.sty
Package: fvextra 2023/11/28 v1.6.1 fvextra - extensions and patches for fancyvr
b

(/Library/Application Support/MiKTeX/texmfs/install/tex/latex/etoolbox/etoolbox
.sty
Package: etoolbox 2020/10/05 v2.5k e-TeX tools for LaTeX (JAW)
\etb@tempcnta=\count189
)
(/Users/<USER>/Library/Application Support/MiKTeX/texmfs/install/tex/late
x/fancyvrb/fancyvrb.sty
Package: fancyvrb 2023/11/06 4.5b verbatim text (tvz,hv)
\FV@CodeLineNo=\count190
\FV@InFile=\read2
\FV@TabBox=\box51
\c@FancyVerbLine=\count191
\FV@StepNumber=\count192
\FV@OutFile=\write3
)
(/Users/<USER>/Library/Application Support/MiKTeX/texmfs/install/tex/late
x/upquote/upquote.sty
Package: upquote 2012/04/19 v1.3 upright-quote and grave-accent glyphs in verba
tim

(/Library/Application Support/MiKTeX/texmfs/install/tex/latex/base/textcomp.sty
Package: textcomp 2020/02/02 v2.0n Standard LaTeX package
))
(/Users/<USER>/Library/Application Support/MiKTeX/texmfs/install/tex/late
x/lineno/lineno.sty
Package: lineno 2023/05/20 line numbers on paragraphs v5.3
Invalid UTF-8 byte or sequence at line 292 replaced by U+FFFD.
\linenopenalty=\count193
\output=\toks18
\linenoprevgraf=\count194
\linenumbersep=\dimen141
\linenumberwidth=\dimen142
\c@linenumber=\count195
\c@pagewiselinenumber=\count196
\c@LN@truepage=\count197
\c@internallinenumber=\count198
\c@internallinenumbers=\count199
\quotelinenumbersep=\dimen143
\bframerule=\dimen144
\bframesep=\dimen145
\bframebox=\box52
LaTeX Info: Redefining \\ on input line 3180.
)
\c@FancyVerbWriteLine=\count266
\c@FancyVerbBufferIndex=\count267
\c@FancyVerbBufferLength=\count268
\c@FancyVerbBufferLine=\count269
\c@FV@oldbufferlength=\count270
\c@FV@TrueTabGroupLevel=\count271
\c@FV@TrueTabCounter=\count272
\FV@TabBox@Group=\box53
\FV@TmpLength=\skip50
\c@FV@HighlightLinesStart=\count273
\c@FV@HighlightLinesStop=\count274
\FV@LoopCount=\count275
\FV@NCharsBox=\box54
\FV@BreakIndent=\dimen146
\FV@BreakIndentNChars=\count276
\FV@BreakSymbolSepLeft=\dimen147
\FV@BreakSymbolSepLeftNChars=\count277
\FV@BreakSymbolSepRight=\dimen148
\FV@BreakSymbolSepRightNChars=\count278
\FV@BreakSymbolIndentLeft=\dimen149
\FV@BreakSymbolIndentLeftNChars=\count279
\FV@BreakSymbolIndentRight=\dimen150
\FV@BreakSymbolIndentRightNChars=\count280
\c@FancyVerbLineBreakLast=\count281
\FV@LineBox=\box55
\FV@LineIndentBox=\box56
\c@FV@BreakBufferDepth=\count282
\FV@LineWidth=\dimen151
)
(/Library/Application Support/MiKTeX/texmfs/install/tex/latex/base/ifthen.sty
Package: ifthen 2022/04/13 v1.1d Standard LaTeX ifthen package (DPC)
)
(/Library/Application Support/MiKTeX/texmfs/install/tex/latex/tools/shellesc.st
y
Package: shellesc 2023/04/15 v1.0d unified shell escape interface for LaTeX
Package shellesc Info: Unrestricted shell escape enabled on input line 75.
)
(/Users/<USER>/Library/Application Support/MiKTeX/texmfs/install/tex/late
x/ifplatform/ifplatform.sty
Package: ifplatform 2017/10/13 v0.4a Testing for the operating system

(/Library/Application Support/MiKTeX/texmfs/install/tex/generic/pdftexcmds/pdft
excmds.sty
Package: pdftexcmds 2020-06-27 v0.33 Utility functions of pdfTeX for LuaTeX (HO
)

(/Users/<USER>/Library/Application Support/MiKTeX/texmfs/install/tex/gene
ric/infwarerr/infwarerr.sty
Package: infwarerr 2019/12/03 v1.5 Providing info/warning/error messages (HO)
)
(/Library/Application Support/MiKTeX/texmfs/install/tex/generic/iftex/iftex.sty
Package: iftex 2022/02/03 v1.0f TeX engine tests
)
Package pdftexcmds Info: \pdf@primitive is available.
Package pdftexcmds Info: \pdf@ifprimitive is available.
Package pdftexcmds Info: \pdfdraftmode not found.
)
(/Users/<USER>/Library/Application Support/MiKTeX/texmfs/install/tex/gene
ric/catchfile/catchfile.sty
Package: catchfile 2019/12/09 v1.8 Catch the contents of a file (HO)

(/Users/<USER>/Library/Application Support/MiKTeX/texmfs/install/tex/gene
ric/etexcmds/etexcmds.sty
Package: etexcmds 2019/12/15 v1.7 Avoid name clashes with e-TeX commands (HO)
))
(/Library/Application Support/MiKTeX/texmfs/install/tex/generic/iftex/ifluatex.
sty
Package: ifluatex 2019/10/25 v1.5 ifluatex legacy package. Use iftex instead.
)
runsystem(uname -s > "test_minted.w18")...executed.

 (test_minted.w18)
runsystem(rm -- "test_minted.w18")...executed.

)
(/Users/<USER>/Library/Application Support/MiKTeX/texmfs/install/tex/late
x/xstring/xstring.sty
(/Users/<USER>/Library/Application Support/MiKTeX/texmfs/install/tex/gene
ric/xstring/xstring.tex
\xs_counta=\count283
\xs_countb=\count284
)
Package: xstring 2023/08/22 v1.86 String manipulations (CT)
)
(/Users/<USER>/Library/Application Support/MiKTeX/texmfs/install/tex/late
x/framed/framed.sty
Package: framed 2011/10/22 v 0.96: framed or shaded text with page breaks
\OuterFrameSep=\skip51
\fb@frw=\dimen152
\fb@frh=\dimen153
\FrameRule=\dimen154
\FrameSep=\dimen155
)
(/Users/<USER>/Library/Application Support/MiKTeX/texmfs/install/tex/late
x/float/float.sty
Package: float 2001/11/08 v1.3d Float enhancements (AL)
\c@float@type=\count285
\float@exts=\toks19
\float@box=\box57
\@float@everytoks=\toks20
\@floatcapt=\box58
)
\minted@appexistsfile=\read3
\minted@bgbox=\box59
\minted@code=\write4
\c@minted@FancyVerbLineTemp=\count286
\c@minted@pygmentizecounter=\count287
\@float@every@listing=\toks21
\c@listing=\count288
)
runsystem(mkdir -p _minted-test_minted)...executed.


(/Library/Application Support/MiKTeX/texmfs/install/tex/latex/xcolor/xcolor.sty
Package: xcolor 2022/06/12 v2.14 LaTeX color extensions (UK)

(/Library/Application Support/MiKTeX/texmfs/install/tex/latex/graphics-cfg/colo
r.cfg
File: color.cfg 2016/01/02 v1.6 sample color configuration
)
Package xcolor Info: Driver file: xetex.def on input line 227.

(/Library/Application Support/MiKTeX/texmfs/install/tex/latex/graphics-def/xete
x.def
File: xetex.def 2022/09/22 v5.0n Graphics/color driver for xetex
)
(/Library/Application Support/MiKTeX/texmfs/install/tex/latex/graphics/mathcolo
r.ltx)
Package xcolor Info: Model `cmy' substituted by `cmy0' on input line 1353.
Package xcolor Info: Model `RGB' extended on input line 1369.
Package xcolor Info: Model `HTML' substituted by `rgb' on input line 1371.
Package xcolor Info: Model `Hsb' substituted by `hsb' on input line 1372.
Package xcolor Info: Model `tHsb' substituted by `hsb' on input line 1373.
Package xcolor Info: Model `HSB' substituted by `hsb' on input line 1374.
Package xcolor Info: Model `Gray' substituted by `gray' on input line 1375.
Package xcolor Info: Model `wave' substituted by `hsb' on input line 1376.
)
runsystem(command -v pygmentize && touch test_minted.aex)...executed.

runsystem(rm test_minted.aex)...executed.


(/Library/Application Support/MiKTeX/texmfs/install/tex/latex/l3backend/l3backe
nd-xetex.def
File: l3backend-xetex.def 2023-04-19 L3 backend support: XeTeX
\g__graphics_track_int=\count289
\l__pdf_internal_box=\box60
\g__pdf_backend_object_int=\count290
\g__pdf_backend_annotation_int=\count291
\g__pdf_backend_link_int=\count292
)
No file test_minted.aux.
\openout1 = `test_minted.aux'.

LaTeX Font Info:    Checking defaults for OML/cmm/m/it on input line 4.
LaTeX Font Info:    ... okay on input line 4.
LaTeX Font Info:    Checking defaults for OMS/cmsy/m/n on input line 4.
LaTeX Font Info:    ... okay on input line 4.
LaTeX Font Info:    Checking defaults for OT1/cmr/m/n on input line 4.
LaTeX Font Info:    ... okay on input line 4.
LaTeX Font Info:    Checking defaults for T1/cmr/m/n on input line 4.
LaTeX Font Info:    ... okay on input line 4.
LaTeX Font Info:    Checking defaults for TS1/cmr/m/n on input line 4.
LaTeX Font Info:    Trying to load font information for TS1+cmr on input line 4
.
(/Library/Application Support/MiKTeX/texmfs/install/tex/latex/base/ts1cmr.fd
File: ts1cmr.fd 2023/04/13 v2.5m Standard LaTeX font definitions
)
LaTeX Font Info:    ... okay on input line 4.
LaTeX Font Info:    Checking defaults for TU/lmr/m/n on input line 4.
LaTeX Font Info:    ... okay on input line 4.
LaTeX Font Info:    Checking defaults for OMX/cmex/m/n on input line 4.
LaTeX Font Info:    ... okay on input line 4.
LaTeX Font Info:    Checking defaults for U/cmr/m/n on input line 4.
LaTeX Font Info:    ... okay on input line 4.
\openout3 = `test_minted.pyg'.

runsystem(pygmentize -S default -f latex -P commandprefix=PYG > _minted-test_mi
nted/default.pygstyle)...executed.


(_minted-test_minted/default.pygstyle)
runsystem(pygmentize -l 'python' -f latex -P commandprefix=PYG -F tokenmerge -P
 stripnl='False' -o _minted-test_minted/CB00F4EDFBC9A0427B9DDA6564383A9FCF6BA73
E0E278C4F87FE47ABD037B2D1.pygtex test_minted.pyg)...executed.


(_minted-test_minted/CB00F4EDFBC9A0427B9DDA6564383A9FCF6BA73E0E278C4F87FE47ABD0
37B2D1.pygtex
LaTeX Font Info:    Font shape `TU/lmtt/bx/n' in size <10> not available
(Font)              Font shape `TU/lmtt/b/n' tried instead on input line 2.
) [1

] (test_minted.aux)
 ***********
LaTeX2e <2023-06-01> patch level 1
L3 programming layer <2023-08-29>
 ***********
runsystem(rm test_minted.pyg)...executed.

 ) 
Here is how much of TeX's memory you used:
 5064 strings out of 410380
 98137 string characters out of 5786836
 1911438 words of memory out of 5000000
 26360 multiletter control sequences out of 15000+600000
 558117 words of font info for 42 fonts, out of 8000000 for 9000
 1348 hyphenation exceptions out of 8191
 75i,5n,81p,259b,472s stack positions out of 10000i,1000n,20000p,200000b,200000s

Output written on test_minted.pdf (1 page).
