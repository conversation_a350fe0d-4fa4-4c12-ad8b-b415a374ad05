{"latex-workshop.latex.tools": [{"name": "xelatex", "command": "xelatex", "args": ["-synctex=1", "-interaction=nonstopmode", "-file-line-error", "-shell-escape", "%DOC%"]}, {"name": "biber", "command": "biber", "args": ["%DOCFILE%"]}], "latex-workshop.latex.recipes": [{"name": "XeLaTeX ➞ Biber ➞ XeLaTeX × 2", "tools": ["xelatex", "biber", "xelatex", "xelatex"]}], "latex-workshop.latex.clean.fileTypes": ["*.aux", "*.bbl", "*.blg", "*.idx", "*.ind", "*.lof", "*.lot", "*.out", "*.toc", "*.acn", "*.acr", "*.alg", "*.glg", "*.glo", "*.gls", "*.ist", "*.fls", "*.log", "*.fdb_latexmk", "*.snm", "*.nav", "*.dvi", "*.synctex.gz", "*.pyg", "*.bcf", "*.run.xml", "_minted-*/**"], "latex-workshop.view.pdf.viewer": "tab", "latex-workshop.latex.autoBuild.run": "onSave", "latex-workshop.latex.outDir": "%DIR%"}