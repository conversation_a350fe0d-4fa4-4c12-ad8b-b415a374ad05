\section{Hadron Gases}
{\color{red} Introduction to hadron gases through fermion gases, and then add the interactions to make a full model. Cassar: QFT math for densities in bracket notation, and bessel function math if useful (?). <PERSON><PERSON><PERSON> has n=2 context. Check Ukranians article for excluded volume}

\subsection{Ideal Fermion Gas}

Before we look into nucleons interacting, we want to consider the simplest case, which is the non-interacting Fermion gas.

{\color{red} Introduce what an ideal fermion gas is, mention that the derivation of the measurable quantities is not trivial at all, and what the goal is by looking at it. We want to build a model to account for all the interactions and stuff. Mention Speed of Sound maybe. Check <PERSON>, maybe <PERSON><PERSON><PERSON>}

To consider a system of Fermions, we use the Dirac Lagrangian density
\begin{equation}
    \mathcal{L}_0=\bar\psi(i\gamma^\mu\partial_\mu-m_N)\psi
    \label{dirac_lagrange}
\end{equation}
where $\psi$ is the Fermion field, $\gamma^\mu$ are the Dirac matrices, the Dirac adjoint is $\bar\psi=\psi^\dagger\gamma^0$, $m_N$ is the Fermion mass (in our case denoted with an \textit{N} since it refers to the mass of nucleons), and the Einstein summation convention is being used. 

In Quantum Field Theory and Statistical Mechanics, it's common to calculate the partition function~\cite{Kapusta_Gale_2023}, which can be calculated from a Lagrangian density in the following way:
\begin{equation}
    \mathcal Z_0=\int \mathcal{D}~\text{exp}\left(\int_0^\beta d\tau\int d^3x\left(\mathcal{L}_0+\mu\psi^\dagger\psi\right)\right)
\label{partition_func_0}
\end{equation}
where $\tau=i t$ is imaginary time, $\beta=1/T$ is inverse temperature, and a chemical potential $\mu$ for the particles is added as a Lagrange multiplier. The notation $\mathcal{D}$ represents functional integration, where $\mathcal{D}=[d\bar\psi][d\psi]= (d\bar\psi_1 d\bar\psi_2 d\bar\psi_3...d\bar\psi_nd\psi_1 d\psi_2 d\psi_3...d\psi_n)$ for $n$ fields.

The expression can be expanded to 
\begin{equation}
    \mathcal Z_0=\int \mathcal{D}~\text{exp}\left(\int_0^\beta d\tau\int d^3x\left(\psi^\dagger(-\partial_\tau-i\gamma^0\bm{\gamma\cdot\nabla}-\gamma^0m_N+\mu)\psi\right)\right)
\end{equation}
To get rid of the imaginary time and space integrals, we can perform a Fourier transform on the Dirac fields to change from time-position space $(\tau, \bm{x})$ to frequency-momentum space $(\omega_n, \bm{p})$. To do this, we perform the substitutions
\begin{equation}
    \psi(\tau, \bm{x})=\frac{1}{V}\sum_n\sum_p e^{i(\bm{p}\cdot\bm{x}+\omega_n\tau)}\tilde\psi_n(\bm{p})~~,
\end{equation}
where $V$ is the volume.

Using the definition of the momentum operator $\bm{p}=-i\bm\nabla$, and the frequency $\omega=-i\partial_\tau$, our partition function becomes
\begin{equation}
    \mathcal Z_0=\int \mathcal{D}~\exp\left(\frac{1}{V}\int_0^\beta d\tau\int d^3x\left(\tilde\psi^\dagger(-i\omega-\gamma^0 \bm{\gamma\cdot p}-\gamma^0m_N+\mu)\tilde\psi\right)\right)~.
\end{equation}
Since the new field does not depend on position-space anymore, we can conduct the two integrals in the exponent such that
\begin{equation}
    \mathcal Z_0=\int \mathcal{D}~\exp\left(\beta\left(\tilde\psi^\dagger(-i\omega-\gamma^0 \bm{\gamma\cdot p}-\gamma^0m_N+\mu)\tilde\psi\right)\right)~.
\end{equation}
Because of Pauli's exclusion principle, it arises that the Fermion fields $\psi$ anticommute with each other, meaning $\{\psi_1, \psi_2\}=\psi_1\psi_2+\psi_2\psi_1=0$. The mathematical tools for this type of algebra was first developed by Grassmann~\cite{grassmann1844}. He showed that
\begin{equation}
    \int d\eta_1^\dagger d\eta_1 d\eta^\dagger_2d\eta_2...d\eta^\dagger_n d\eta_n e^{\eta^\dagger A \eta}=\det A~~,
\end{equation}
which we can apply to our partition function. We can define $A=\beta(-i\omega-\gamma^0 \bm{\gamma\cdot p}-\gamma^0m_N+\mu)$, which is an operator that can be diagonalizable into its eigenvalues $a_n$ to find 
\begin{equation}
     \mathcal Z_0=\det A=\prod_i^n a_i=\exp\left(\sum_i^n\ln(a_i)\right)=\exp[\text{Tr}(\ln(A))]~~.
\end{equation}
Using the definitions of the Dirac matrices and applying the logarithm to the partition function~\cite{Kapusta_Gale_2023}, we find
\begin{equation}
    \ln(\mathcal Z_0)=\text{Tr}(\ln(A))=2\sum_n\sum_{\bm p}\ln(\beta^2(\omega_n+i\mu)^2+E^2)
\end{equation}
where $E=\sqrt{p^2+m_N^2}$, and the 2 rises from the summation of both the positive and negative frequencies. This can be rewritten as 
\begin{equation}
    \ln(\mathcal Z_0)=\sum_n\sum_{\bm p}\left(\ln(\beta^2(\omega_n^2+(E-\mu)^2))+\ln(\beta^2(\omega_n^2+(E+\mu)^2))\right)
\end{equation}
We can introduce the identity 
\begin{equation}
\ln\left((2n+1)^2\pi^2+\beta^2(E\pm\mu)^2\right)=\int_1^{\beta^2(E\pm\mu)^2}\frac{d\theta^2}{\theta^2+(2n+1)^2\pi^2}+\ln\left(1+(2n+1)^2\pi^2\right)
\end{equation}
Applying the identity, integrating the result over $\theta$, and dropping the constant term, we get our result {\color{red} Add more context, full derivation in Arzate (Roberts does in in less detail).}
\begin{equation}
    \ln(\mathcal Z_0)=2V\int\frac{d^3p}{(2\pi)^3}\left(\beta E+\ln\left(e^{-\beta(E-\mu)}+1\right)+\ln\left(e^{-\beta(E+\mu)}+1\right)\right)
\end{equation}
With this quantity, we can calculate useful quantities such as pressure, which relates to the partition function as
\begin{equation}
    P_0=\frac{\partial}{\partial V} \left(\frac{\ln \mathcal Z_0}{\beta}\right)~~,
\end{equation}
which for our case is 
\begin{equation}
    P_0=2 T \int\frac{d^3p}{(2\pi)^3}\left[\beta E+\ln\left(e^{-\beta(E-\mu)}+1\right)+\ln\left(e^{-\beta(E+\mu)}+1\right)\right]
\label{p_FG_1}
\end{equation}
The 2 in front of the integral corresponds with the degeneracy of the system. Since the particles can have spin up and down, the degeneracy is $g=2$, but if there were more types of particles, the degeneracy could be higher. However,  this integral contains the vacuum energy, which is the first term in the integral. This term is often neglected in Quantum Field Theory since it diverges. 

\subsection{Walecka Model}

{\color{red}Add context on the fields to be accounted for. Yukawa potentials and whatnot. Why sigma/omega fields? What is scalar density? What is baryon (particle) density and vector (field) density? Attraction is VanDerWaals (?), repulsion is excluded volume. }

A Lagrangian can be formed to take into account the interactions of the nucleons as well as the vector and scalar fields of the mesons. The Lagrangian we choose to use is the Walecka Model Lagrangian~\cite{walecka1974}, which couples a repulsive vector field $\omega$ and an attractive scalar field $\sigma$ to the nucleons. The nucleons have a rest mass $m_N$, and coupling constants $g_\omega$ and $g_\sigma$ to the vector and scalar fields. 
\begin{equation}
\begin{split}
    \mathcal{L}_W=\bar{\psi}(&i\gamma^\mu \partial_\mu-m_N+g_\sigma\sigma-g_\omega\gamma^\mu\omega_\mu)\psi\\
    &+\frac{1}{2}\left(\partial_\mu\sigma\partial^\mu\sigma-m_\sigma^2\sigma^2\right)-\frac{1}{4}F^{\mu\nu}F_{\mu\nu}+\frac{1}{2}m_\omega^2\omega_\mu\omega^\mu
\end{split}
\label{walecka_0}
\end{equation}
where $\psi$ is the Dirac field, $\gamma^\mu$ are the Dirac matrices, and $W$ is the field strength tensor, which is constructed as 
\begin{equation}
    F_{\mu\nu}=\partial_\mu\omega_\nu-\partial_\nu\omega_\mu~~.
\end{equation}
To understand each term in the model, we recognize that the Lagrangian can be split into the non-interacting part $\mathcal{L}_0$ and the interactions $\mathcal{L}_I$ such that $\mathcal{L}=\mathcal{L}_0+\mathcal{L}_I$ and 
\begin{equation}
    \begin{split}
        &\mathcal{L}_0= \bar{\psi}(i\gamma^\mu \partial_\mu-m_N)\psi\\
        &\mathcal{L}_I=\bar{\psi}(g_\sigma\sigma-g_\omega\gamma^\mu\omega_\mu)\psi+\frac{1}{2}\left(\partial_\mu\sigma\partial^\mu\sigma-m_\sigma^2\sigma^2\right)-\frac{1}{4}F^{\mu\nu}F_{\mu\nu}+\frac{1}{2}m_\omega^2\omega_\mu\omega^\mu
    \end{split}
\end{equation}
where we can see that $\mathcal{L}_0$ is the Dirac Lagrangian for a Fermion Gas, just as we saw in Eq.~\eqref{dirac_lagrange}.

We can find the equations of motion for the $\sigma$ and $\omega$ fields. By applying Euler-Lagrange equations, we find the equations
\begin{equation}
    \begin{split}
        \frac{\partial\mathcal{L}_W}{\partial\sigma}-\partial_\mu\left(\frac{\partial\mathcal{L}_W}{\partial(\partial_\mu\sigma)}\right)=\bar\psi g_\sigma\psi-m_\sigma^2\sigma - \partial_\mu\partial^\mu\sigma=0\\
        \frac{\partial\mathcal{L}_W}{\partial\omega_\mu}-\partial_\mu\left(\frac{\partial\mathcal{L}_W}{\partial(\partial_\mu\omega)}\right)=\bar\psi g_\omega\gamma^\mu\psi-\partial_\nu F^{\nu\mu}+m_\omega^2\omega^\mu=0
    \end{split}~~,
    \label{eq_motion_0}
\end{equation}
which are complicated to solve as they are nonlinear. However, these equations can be linearized by making an assumption that ignores the kinetic energy terms, which we're about to do. The vector and scalar fields are produced by the nucleons, and we can use the rotational symmetry to write the fields as 
\begin{equation}
\begin{split}
    \sigma &= \bar{\sigma}+\sigma'\\
    \omega_\mu &= \delta_{\mu 0}\bar{\omega}+\omega_\mu'
\end{split}
\label{gap_eq}~~,
\end{equation}
where $\bar{\sigma}$ and $\bar{\omega}$ are the ensemble field averages, and where $\sigma'$ and  $\omega'$ are the fluctuations in the fields. Rotational symmetry is assumed in the $\omega$ field, which adds that $\delta$ to make sure that the ensemble average only gets included once. Throughout this study and as is common in the Walecka model, we take the mean-field approximation, which gets rid of fluctuations in the fields, leaving only the ensemble averages $\bar{\sigma}$ and $\bar{\omega}$ to take into account, and removing the kinetic energy terms of the scalar and vector fields in $\mathcal{L}_W$. The resulting interacting Lagrangian is now
\begin{equation}
    \mathcal{L}_I=\bar{\psi}(g_\sigma\bar\sigma-g_\omega\gamma^0\bar\omega)\psi-\frac{1}{2}m_\sigma^2\bar\sigma^2+\frac{1}{2}m_\omega^2\bar\omega^2
\end{equation}

With this approximation, the equations of motion from Eq.~\eqref{eq_motion_0} can be further simplified to 
\begin{equation}
    \begin{split}
        \bar\sigma=\frac{g_\sigma}{m_\sigma^2}\langle\bar\psi\psi\rangle\\
        \bar\omega=\frac{g_\omega}{m_\omega^2}\langle\psi^\dagger\psi\rangle
    \end{split}~~,
    \label{Eqs_of_motion}
\end{equation}
which hints at a shift from field quantities to the expected values, which are particle densities. This will be done later in the chapter.

To find useful thermodynamic quantities such as pressure or binding energy, we form the partition function, which we calculate in a similar way as in Eq.~\eqref{partition_func_0}:

\begin{equation}
\begin{split}
    Z=&\int [d\bar\psi_p][d\psi_p][d\bar\psi_n][d\psi_n][d\sigma][d\omega_\mu]\\
    &\times\text{exp}\left(\int_0^\beta d\tau\int d^3x\left(\mathcal{L}_W+\mu_p\psi_p^\dagger\psi_p+\mu_n\psi_n^\dagger\psi_n\right)\right)
\end{split}
\label{partition_func}
\end{equation}
where the subscripts $p$ denote proton and $n$ denote neutron, and $\psi_p+\psi_n=\psi$. In our case, we concentrate on symmetric matter, which assumes equal chemical potential between neutrons and protons $\mu=\mu_p=\mu_n$. 

Using Eqs.\eqref{gap_eq} we find the argument inside the integrals in the exponential of the partition function to be 
\begin{equation}
\bar\psi(i\gamma^\mu\partial_\mu-m_N+g_\sigma\bar\sigma+(\mu-g_\omega\bar\omega)\gamma^0)\psi-\frac{1}{2}m_\sigma^2\bar\sigma^2+\frac{1}{2}m_\omega^2\bar\omega^2~~.
\label{partition_argument_1}
\end{equation}
Here, we can make the fermion field part look like the Dirac Lagrangian from Eq.~\eqref{dirac_lagrange} with an additional chemical potential part, to reproduce an ideal gas, which will be the non-interacting part of the system. To do that,  we define the effective mass and effective chemical potential as
\begin{equation}
\begin{split}
    m^*=~&m_N-g_\sigma\bar\sigma\\
    \mu^*=~&\mu-g_\omega\bar\omega
\end{split}
\label{eff_eqs_1}
\end{equation}
which makes \eqref{partition_argument_1} become
\begin{equation}
    \bar\psi(i\gamma^\mu\partial_\mu-m^*+\mu^*\gamma^0)\psi-\frac{1}{2}m_\sigma^2\bar\sigma^2+\frac{1}{2}m_\omega^2\bar\omega^2
\label{partition_argument}~~.
\end{equation}
The advantage of this is that now the first part of the equation resembles the Dirac Lagrangian term from Eq.~\eqref{partition_func_0} but has effective mass and effective chemical potential instead of mass and chemical potential. The part in the parenthesis will act as a fermion gas, and the remaining terms will be the interactions. We can denote {$\mathcal{D}=[d\bar\psi_p][d\psi_p][d\bar\psi_n][d\psi_n][d\sigma][d\omega_\mu]$} to simplify the notation in the differentials in the initial integral, and define the field contributions as $\xi=-\frac{1}{2}m_\sigma^2\bar\sigma^2+\frac{1}{2}m_\omega^2\bar\omega^2$ to simplify the partition function as 
\begin{equation}
    Z=\int\mathcal{D}\exp\left(\int_0^\beta d\tau\int d^3x\left[  \bar\psi(i\gamma^\mu\partial_\mu-m^*+\mu^*\gamma^0)\psi + \xi\right]\right)
\end{equation}
The contribution part $\xi$ can be carried out of the initial integral as it doesn't depend on any of the fields since the values are now mean fields. For this term, the spatial part of the integral will turn into volume $V$, and the complex time integral will turn into $\beta$. Our partition function now looks like 
\begin{equation}
    Z=e^{\xi\beta V}\int\mathcal{D}\exp\left(\int_0^\beta d\tau\int d^3x\left[  \bar\psi(i\gamma^\mu\partial_\mu-m^*+\mu^*\gamma^0)\psi \right]\right)~.
\end{equation}

A useful quantity to calculate out of the partition function is its logarithm, which we can see corresponds to
\begin{equation}
    \ln Z=\xi\beta V+ \ln \left(\int\mathcal{D}\exp\left(\int_0^\beta d\tau\int d^3x\left[  \bar\psi(i\gamma^\mu\partial_\mu-m^*+\mu^*\gamma^0)\psi \right]\right)\right)~~,
\end{equation}
where the first term accounts for the interactions and the second term accounts for the non-interacting part of the system.
We then calculate the pressure using standard thermodynamic quantities
\begin{equation}
    P=\frac{\partial}{\partial V} \left(\frac{\ln Z}{\beta}\right)=\xi + P_0
\end{equation}
where $P_0$ will be the pressure caused by the non-interacting Fermion gas formed between the nucleons. This can also be rewritten as 
\begin{equation}
    P=P_0-\frac{1}{2}m_\sigma^2\bar\sigma^2+\frac{1}{2}m_\omega^2\bar\omega^2
    \label{presure_w_fields}
\end{equation}
The mean field values are by definition the quantities that minimize the pressure for two nucleon interactions, which is given by 
\begin{equation}
    \begin{split}
        \frac{\partial P}{\partial \bar\sigma}= \frac{\partial P_0}{\partial m^*}\frac{\partial m^*}{\partial \bar \sigma}-m_\sigma^2\bar\sigma=&\frac{\partial P_0}{\partial m^*}(-g_\sigma)-m_\sigma^2\bar\sigma = 0\\
        \frac{\partial P}{\partial \bar\omega}= \frac{\partial P_0}{\partial \mu^*}\frac{\partial \mu^*}{\partial \bar \omega}+m_\omega^2\bar\omega=&\frac{\partial P_0}{\partial \mu^*}(-g_\omega)+m_\omega^2\bar\omega = 0
    \end{split}
\end{equation}
which leads to the average fields to be 
\begin{equation}
    \begin{split}
        \bar\sigma&=-\frac{g_\sigma}{m_\sigma^2}\frac{\partial P_0}{\partial m^*}\\
        \bar\omega&=\frac{g_\omega}{m_\omega^2}\frac{\partial P_0}{\partial \mu^*}
    \end{split}
    \label{fields_densities_1}
\end{equation}
As hinted before, we now want to shift to using particle densities instead of field averages. We define the scalar density $n_ \sigma=\langle\bar\psi\psi\rangle$ and the vector density $n_\omega=\langle\psi^\dagger\psi\rangle$. We then use these definitions and Eqs.~\eqref{Eqs_of_motion} to find the convenient relations: 
\begin{equation}
\begin{split}
    n_\sigma &=- \frac{\partial P_0}{\partial m^*}\\
    n_\omega&=\frac{\partial P_0}{\partial \mu^*}
\end{split}
\label{fields_densities}
\end{equation}
or relating to Eq.~\eqref{fields_densities_1}, we get our direct relationship between the mean fields and particle densities: 
\begin{equation}
\bar\sigma=-\frac{g_\sigma}{m_\sigma^2}n_\sigma
\label{sigma_field}
\end{equation}
\begin{equation}
\bar\omega=\frac{g_\omega}{m_\omega^2}n_\omega
\label{omega_field}
\end{equation}
For convenience, we define the constants
\begin{equation}
\begin{split}
    G_\sigma=\left(\frac{g_\sigma}{m_\sigma}\right)^2\\
    G_\omega=\left(\frac{g_\omega}{m_\omega}\right)^2
\end{split}
\end{equation}
which tells us how much the fields are coupled to the nucleons. Using these definitions, we rewrite our effective mass and chemical potential definitions from Eq.\eqref{eff_eqs_1} to get
\begin{equation}
\begin{split}
    m^*=~&m_N-G_\sigma n_\sigma\\
    \mu^*=~&\mu-G_\omega n_\omega~.
\end{split}
\label{eff_eqs}
\end{equation}
We also rewrite the pressure from Eq.~\ref{presure_w_fields} to be 
\begin{equation}
    P(\mu, T) =P_0(m^*, \mu^*, T) -\frac{1}{2}G_\sigma n_\sigma^2 +\frac{1}{2}G_\omega n_\omega^2
    \label{P_eq}
\end{equation}

The non-interacting fermion gas pressure is the same as Eq.~\eqref{p_FG_1}, with the exception that now the mass and chemical potential are changed for effective quantities due to interactions. Additionally, the vacuum term is neglected as it is often practice in Field Theory, otherwise the term would diverge when computing the integral. The resulting non-interacting pressure is 
\begin{equation}
    P_0=g T \int\frac{d^3p}{(2\pi)^3}\left[\ln\left(e^{-\beta(E^*-\mu^*)}+1\right)+\ln\left(e^{-\beta(E^*+\mu^*)}+1\right)\right]
\label{p_FG}
\end{equation}
where $g$ is our degeneracy factor, and the effective energy $E^*$ depends on momentum $E^*=\sqrt{p^2+m^{*2}}$.

Using Eqs.~\eqref{fields_densities} and Eq.\eqref{p_FG}, we can find the densities
\begin{equation}
    n_\omega=g \int\frac{d^3p}{(2\pi)^3}\left( f_{FD}-\bar  f_{FD}\right)
    \label{vector_density}
\end{equation}
\begin{equation}
    n_\sigma=g \int\frac{d^3p}{(2\pi)^3}\frac{m^*}{E^*}\left( f_{FD}+\bar  f_{FD}\right)
    \label{scalar_density}
\end{equation}
where the terms in the parenthesis for both expressions are the Fermi-Dirac distributions for particles 
\begin{equation}
    f_{FD}\equiv f_{FD}(E^*, \mu^*, T)=\frac{1}{e^{\frac{E^*-\mu^*}{T}}+1}
    \label{Fermi_Dirac_Distribution}
\end{equation}
and antiparticles $\bar f_{FD}=f_{FD}(E^*, -\mu^*, T)$.

\subsection{Zero Temperature Limit}

The non-interacting pressure from Eq.~\eqref{p_FG} can be simplified under a zero-temperature limit. We first use  rotational symmetry $d^3p=4\pi p^2 dp$, which results in 
\begin{equation}
    P_0=\frac{gT}{2\pi^2} \int_0^\infty dp~p^2\left[\ln\left(e^{-\beta(E^*-\mu^*)}+1\right)+\ln\left(e^{-\beta(E^*+\mu^*)}+1\right)\right]
\end{equation}
whereas before, we're using the relativistic dispersion relation $E^*=\sqrt{p^2+(m^*)^2}$. This expression can be further simplified by doing integration by parts, integrating $p^2$ and deriving the logarithm term. The result is {\color{red}Maybe move this expression to the previous subsection, might be useful for other cases.}
\begin{equation}
    P_0=\frac{g}{6\pi^2} \int_0^\infty dp\frac{p^4}{\sqrt{p^2+(m^*)^2}}\left( f_{FD}+\bar  f_{FD}\right)
\label{p_FG_0}
\end{equation}
where the terms in the parenthesis are the Fermi-Dirac distributions for particles and antiparticles respectively.

Now we apply the zero temperature limit, which turns the Fermi-Dirac distributions into step functions. The distribution for particles produces 1 for $\mu^*>E^*$, and 0 otherwise, and gets rid of the antiparticle distribution since for the $T\rightarrow0$ limit, the distribution always gives zero. In other words, the momentum now ranges up to a specific value $p_F$ named "Fermi momentum", and there are no antiparticles to consider when calculating the non-interacting pressure. The effective chemical potential becomes the effective Fermi Energy
\begin{equation}
    \mu^*\rightarrow E^*_F=\sqrt{p_F^2+(m^{*})^2}~~.
\end{equation}
Our expression for non-interacting pressure from Eq.~\eqref{p_FG_0} now becomes
\begin{equation}
    P_0=\frac{g}{6\pi^2} \int_0^{p_F} dp\frac{p^4}{\sqrt{p^2+(m^*)^2}}
\end{equation}
which can be solved if we make a variable transformation. If we integrate over energy instead of momentum, we  use $p=\sqrt{(E^*)^2-(m^*)^2}$ and $p~dp=E^*~dE^*$ to make the transformation
\begin{equation}
    P_0=\frac{g}{6\pi^2} \int_{m^*}^{E^*_F} dE^*\left((E^*)^2-(m^*)^2\right)^{3/2}
\end{equation}
which gives our final result for our non-interacting pressure. The result of this integral combined with Eq.~\eqref{p_FG} gives us our final expression for pressure at the zero temperature limit 
\begin{equation}
\begin{split}
    P=\frac{g}{16\pi^2}&\left[\frac{2}{3}E^*_Fp_F^3-m^{*2} E^*_Fp_F+m^{*4}\ln\left(\frac{E^*_F+p_F}{m^*}\right)\right]\\
    &-\frac{1}{2}G_\sigma n_\sigma^2+\frac{1}{2}G_\omega n_\omega^2~~.
\end{split}
\label{P_T_0}
\end{equation}
The vector density from Eq.~\eqref{vector_density} under the zero temperature limit will be 
\begin{equation}
    n_\omega=\frac{g}{2\pi^2}\int_0^{p_F}dp~p^2=\frac{g}{6\pi^2}p_F^3
\end{equation}
and the scalar density is calculated in a similar way to pressure, where we integrate over effective energy: 
\begin{equation}
    n_\sigma=\frac{g}{2\pi^2}\int_0^{p_F} dp~p^2\frac{m^*}{E^*}=\frac{g}{2\pi^2}\int_{m^*}^{E^*_F}dE^*m^*\sqrt{(E^*)^2-(m^*)^2}
\end{equation}
which gives us our final result
\begin{equation}
    n_\sigma=g\frac{m^*}{4\pi^2}\left[E_F^*p_F-(m^{*})^2\ln\left(\frac{E^*_F+p_F}{m^*}\right)\right]
    \label{n_s_T_0}
\end{equation}

We can also calculate the average energy of the system using the thermal identity $\epsilon=-P+TS+\mu n_\omega$, where the entropy density term $s$ is ignored at zero temperature and $\mu=\mu^*+G_\omega n_\omega$. The energy becomes
\begin{equation}
\begin{split}
    \epsilon=\frac{g}{16\pi^2}&\left[2E^*_Fp_F^3+(m^{*})^2 E^*_Fp_F-(m^{*})^4\ln\left(\frac{E^*_F+p_F}{m^*}\right)\right]\\
    &+\frac{1}{2}G_\sigma n_\sigma^2+\frac{1}{2}G_\omega n_\omega^2
\end{split}
\label{E_T_0}
\end{equation}

To solve this numerically and calculate both pressure and energy, we use the Fermi momentum $p_F$ as our independent variable. We encounter a recursive definition between the effective mass and the scalar density in Eq.\eqref{eff_eqs} and Eq.\eqref{n_s_T_0}. To solve this, we implement a relaxation iterative method to calculate $m^*$ to find converging values using a relaxation factor $\alpha=0.5$ as 
\begin{equation}
    m^*\rightarrow\alpha m^*+(1-\alpha)(m - G_s n_s)~~,
\end{equation}
and running until $m^*$ converges to its solution. The tolerance used is 0.001.


\begin{figure}
\begin{subfigure}{.5\textwidth}
  \centering
  \includegraphics[width=\linewidth]{Figures/ZeroTemp_E.png}
  \caption{Energy}
  \label{fig:sfig1}
\end{subfigure}%
\begin{subfigure}{.5\textwidth}
  \centering
  \includegraphics[width=\linewidth]{Figures/ZeroTemp_P.png}
  \caption{Pressure}
  \label{fig:sfig2}
\end{subfigure} \\
\caption[Energy Plot]{Energy and Pressure of two nucleons at $T\rightarrow 0$ limit using the Walecka Model under the mean-field approximation.}
\label{zeroTempPlots}
\end{figure}

Using the parameters $m^*=939~\text{MeV}$, $m_\sigma=550~\text{MeV}$, $m_\omega=783~\text{MeV}$, $g_\sigma^2/4\pi=9.537$, and $g_\omega^2/4\pi=14.717$, the results obtained can be seen in Fig.~\ref{zeroTempPlots}.

\subsection{Finite Temperature}

The integrals for the Fermi-Gas Pressure in Eq.~\eqref{p_FG} and the scalar and vector densities in Eq.~\eqref{scalar_density} and Eq.~\eqref{vector_density} respectively, do not simplify easily and must be solved numerically to calculate the total pressure at a finite temperature. Assuming spherical momentum symmetry, we establish that $d^3p=4\pi p^2dp$.

We use the \textbf{scipy} library to evaluate the integrals and some of the functions that diverge to large values inside the integrals. We use \textit{quad} to solve the integrals using Gaussian quadrature, \textit{expit(x)} to evaluate the $(e^{-x}+1)^{-1}$ inside the densities definitions, and \textit{logsumexp([0,x])} to evaluate $\ln(1+e^x)$ in the pressure integrals.

We create a function \textit{calc\_e(p, m)} to calculate the relativistic energy dispersion relation $E=\sqrt{p^2+m_N^2}$.

\begin{minted}{python}
# Calculate Fermi Gas pressure from mu effective and temperature
def calc_Fermion_Gas(mu_eff, eff_m, T):

    def f(p): # Function inside the integral
        eff_e = calc_e(p, eff_m)                # Effective energy
        return p**2 * (logsumexp([0,-(eff_e-mu_eff)/T]) + 
            logsumexp([0,-(eff_e+mu_eff)/T])) 
    
    integral, error = quad(f, 0, np.inf, limit=1000) # P integral
    return g/(2 * pi**2) * T * integral 
    
# Scalar density
def n_s(mu_eff, eff_m, T): 
    def f(p):
        eff_e = calc_e(p, eff_m)           # Effective energy
        return p**2 * eff_m/eff_e * (expit(-(eff_e-mu_eff)/T) + 
            expit(-(eff_e+mu_eff)/T))    
    integral, error = quad(f, 0, np.inf, limit=1000) # n_s integral
    return g/(2 * pi**2) * integral 

# Vector density
def n_v(mu_eff, eff_m, T): 
    def f(p):
        eff_e = calc_e(p, eff_m)                # Effective energy
        return p**2 * (expit(-(eff_e-mu_eff)/T) -
            expit(-(eff_e+mu_eff)/T))    
    integral, error = quad(f, 0, np.inf, limit=1000) # n_v integral
    return g/(2 * pi**2) * integral 
\end{minted}

In this case, the chosen independent variable to run through is the effective mass $m^*$ after finding that the results converged faster than when choosing effective potential $\mu^*$ or chemical potential $\mu$ as the independent variable. 

To find effective chemical potential we use the effective mass definition from Eq.~\eqref{eff_eqs} to find the value of the scalar density $n_\sigma$, and then iterate for effective chemical potential values to approximate the scalar density from its integral definition in Eq.~\eqref{scalar_density}. By testing different densities calculated by the effective chemical potentials, we approximate until a value with the desired accuracy is found.

\begin{minted}{python}
n_s_val = (m_0 - eff_mass_arr[i]) * (m_s/g_s)**2 

while(error_a > maxError and error_b > maxError):

    n_s_a = n_s(eff_mass, eff_mu - d_mu, T)
    n_s_b = n_s(eff_mass, eff_mu + d_mu, T)

    error_a = abs(n_s_a - n_s_val) / n_s_val
    error_b = abs(n_s_b - n_s_val) / n_s_val

    if error_a > error_b:
        if prev == 'a':
            d_mu *= 0.5
        prev = 'b'
        eff_mu += d_mu
    else:
        if prev == 'b':
            d_mu *= 0.5
        prev = 'a'
        eff_mu -= d_mu
\end{minted}

This method was significantly faster than a relaxation method using other quantities as independent variables.

\begin{figure}
\begin{subfigure}{.5\textwidth}
  \centering
  \includegraphics[width=\linewidth]{Figures/FiniteTemp_P_Contribution.png}
  \caption{}
  \label{fig:nonlinear1}
\end{subfigure}%
\begin{subfigure}{.5\textwidth}
  \centering
  \includegraphics[width=\linewidth]{Figures/FiniteTemp_P_Contribution_zoom.png}
  \caption{}
  \label{fig:nonlinear2}
\end{subfigure} \\
    \caption{Finite temperature contributions to pressure at $T=10~\text{MeV}$.}
    \label{fig:P_contributions}
\end{figure}
For our case, we have degeneracy $g=4$ due to the spin and isospin properties of nucleons. Using the same parameters as before:  $m^*=939~\text{MeV}$, $m_\sigma=550~\text{MeV}$, $m_\omega=783~\text{MeV}$, $g_\sigma^2/4\pi=9.537$, and $g_\omega^2/4\pi=14.717$; and running the effective mass from $400~\text{MeV}$ to the nucleon rest mass $m_N$. The result of visualizing each contribution factor of the pressure can be seen in Fig.~\ref{fig:P_contributions}, where $T=10~\text{MeV}$. This plot shows us the significance of including interactions in our model, since the contributions are very significant compared to the non-interacting pressure. {\color{red}Add that at high density limit, the field interaction terms dominate in both pressure and energy.}

\begin{figure}
    \centering
    \includegraphics[width=0.6\linewidth]{Figures/FiniteTemp_P.png}
    \caption{Pressure per baryon density at finite temperature numerically calculated in Python. A black dashed line at 0 is added to see the liquid-gas phase transition.}
    \label{fig:P_plot}
\end{figure}

We check to see how the pressure changes with temperature in Fig.~\ref{fig:P_plot}, where pressure with 5 different temperatures is plotted. We also see in the figure that when $T\rightarrow 0$ the pressure greatly resembles the one seen in Fig.~\ref{fig:sfig2} as expected. 


\subsection{Entropy Density and Binding Energy for Finite Temperatures}

\begin{figure}
    \begin{subfigure}{.5\textwidth}
  \centering
  \includegraphics[width=\linewidth]{Figures/Binding_Energy.png}
  \caption{}
\end{subfigure}
\begin{subfigure}{.5\textwidth}
  \centering
  \includegraphics[width=\linewidth]{Figures/EntropyDensity.png}
  \caption{}
\end{subfigure}
    \caption{Binding Energy and Entropy Density for finite temperature. }
    \label{fig:binding_energy}
\end{figure}


To calculate the entropy density $s=S/V$, where $S$ is entropy and $V$ is the volume of the system, we use the thermodynamic identity
\begin{equation}
    s\equiv\frac{\partial P}{\partial T}=\frac{\partial P_0}{\partial T}
\end{equation}
Using Eq.~\eqref{p_FG}, this evaluates to 
\begin{equation}
    s=\frac{\partial P_0}{\partial T}=
    \frac{1}{T}\left(P_0 + g\int\frac{d^3p}{(2\pi)^3}E^*\left(f_{FD}+\bar f_{FD}\right)-\mu^*n_\omega\right)~~.
    \label{entropy_0}
\end{equation}
Then we can use the entropy density to find the binding energy using the thermodynamic identity $\epsilon=-P+Ts+\mu n_\omega$, 
\begin{equation}
    \epsilon= g\int\frac{d^3p}{(2\pi)^3}E^*\left(f_{FD}+\bar f_{FD}\right)+\frac{1}{2}G_\omega n_\omega+\frac{1}{2}G_\sigma n_\sigma~~,
\end{equation}
where we can see that the binding energy can be separated into its non-interacting part $\epsilon_0$ (the integral) and interacting part $\epsilon_I$ (the density contributions) such that $\epsilon=\epsilon_0+\epsilon_I$, just like the pressure does. With this definition of the non-interacting binding energy, the thermodynamic expression used to calculate Eq.~\eqref{entropy_0} simplifies to
\begin{equation}
    \epsilon_0=-P_0+Ts+\mu^*n_\omega
\end{equation}
which relates the non-interacting quantities, and 
\begin{equation}
\epsilon_I=-P_I+G_\omega n_\omega^2
\label{e_I}
\end{equation}
which relates the interacting parts. We can see the binding energy for finite temperatures in Fig.~\ref{fig:binding_energy}, where in the  $T\rightarrow 0$ limit the minimum is at near $0.16~\text{fm}^{-3}$. Here, matter is the most stable at small clumps. The pressure at the stable point goes to zero. 

In this case, the simplest way to calculate $s$ and $\epsilon$ is to calculate $\epsilon_0$ with the integral definition, $\epsilon_I$ with Eq.~\eqref{e_I} and combine them to get the binding energy. And with this, to calculate the entropy density using the thermodynamic identity. {\color{red} Maybe reword}


\subsection{Liquid-Gas Phase Transition}
\label{Liquid_gas_phase}

To interpret the results, we can see by Fig.~\ref{zeroTempPlots}, at the limit $T\rightarrow0$, the baryon density is most stable at a specific value rather than at zero. It turns out that at zero temperature the nucleons tend to form clumps of a specific density. We can look at Fig.~\ref{fig:P_plot} that when the energy is minimized at slightly above $0.15~\text{fm}^{-3}$ the pressure goes to 0. If there was slightly more density then the pressure becomes positive, which makes the nucleons have repulsion, pushing them apart and reducing the density (since it's the same amount of particles but the volume increases). On the contrary, if there were slightly less density, the pressure would be negative, which produces attraction between the particles, pulling them together to increase the baryon density. As we can see, this will be a stable point. 

We now look at the region $0<T<T_C$, where there is still a point at zero pressure ($T_c$ will be the critical temperature at which this doesn't happen anymore). As temperature rises, the particles gain energy in the form of momentum, which will make them move or "wiggle". This behavior resembles the one of a liquid. There is also a new region of interest in Fig.~\ref{fig:P_plot}, which is where the baryon density is very low but the pressure is above 0. This low-density part of the system that repels each other behaves is the part we call the gas. As the temperature increases, this region increases and gains importance. 

At the critical temperature $T_c$ which appears to be around $15~\text{MeV}$, the stable point for baryon density is no longer at $P=0$, meaning that a transition has occurred. At $T>T_c$ the pressure is always positive, meaning that the whole system is now a gas. This is why the transition is called "liquid-gas" phase transition, since at low temperatures the system behaves more liquid-like, and then it fully transitions to a gas once the critical temperature has been achieved. 

\begin{figure}
\begin{subfigure}{.5\textwidth}
  \centering
  \includegraphics[width=\linewidth]{Figures/mu_vs_P_Walecka.png}
  \caption{}
\end{subfigure}%
\begin{subfigure}{.5\textwidth}
  \centering
  \includegraphics[width=\linewidth]{Figures/mu_E_Walecka.png}
  \caption{}
\end{subfigure} \\
    \caption{Pressure and binding energy as functions of chemical potential. These quantities are multivalued before the phase transition ($T<T_c\approx15~\text{MeV}$) and single-valued after the transition.}
    \label{fig:phase_transitions}
\end{figure}

To directly visualize the quantities before and after the transition, we can see Fig.~\ref{fig:phase_transitions}, where multiple values of pressure and binding energy emerge from a single chemical potential value before the phase transition happens. 


\subsection{Multiple Body interactions}

The Lagrangian from Eq.~\eqref{walecka_0} implements scalar interactions between fields as squared terms ($\sigma^2$), which represent two-body interactions. However, this theory is renormalizable and more interactions can be accounted for. By including cubed and quartic terms, these can be used for better fitting of empirical data. The new Lagrangian would become
\begin{equation}
    \mathcal{L}'_W=\mathcal{L}_W-\frac{1}{3}bm_N(g_\sigma\sigma)^3-\frac{1}{4}c(g_\sigma\sigma)^4~~,
    \label{multi_body_L}
\end{equation}
where $b$ and $c$ are the nonlinear parameters to fit. After adding corrections~\cite{Kapusta_Gale_2023}, we once again do the Euler-Lagrange equations and the mean-field approach to find an expression for the scalar field:
\begin{equation}
  \frac{\partial \mathcal{L}_W'}{\partial\sigma}=g_\sigma n_\sigma-m_\sigma^2\bar\sigma-b m_N g_\sigma^3\bar\sigma^2-c g_\sigma^4\bar\sigma^3=0~~,
  \label{multi_body_density}
\end{equation}
which is no longer a linear relationship between the mean field value and the density. Calculating the partition function, we find that the interaction terms that appear in the pressure equation are the same as the ones that are directly included in the Lagrangian. The pressure changes to include these interactions:
\begin{equation}
    P(\mu, T)=P_0(\mu^*, T)  +\frac{1}{2}G_\omega n_\omega^2-\frac{1}{2}m_\sigma^2\bar\sigma^2-\frac{1}{3}b m_N (g_\sigma\bar\sigma)^3+\frac{1}{4}cm_N (g_\sigma\bar\sigma)^4  
    \label{P_eq_nonlinear}
\end{equation}

\begin{figure}
    \centering
    \includegraphics[width=0.8\linewidth]{Figures/FiniteTemp_P_Nonlinear_2.png}
    \caption{Third-body interactions at nonlinear parameter $b=7.95\times10^{-3}$ and coupling constants $g^2_\sigma/4\pi=6.003$ and $g_\omega^2/4\pi=5.948$.}
    \label{fig:E_nonlinear_plot}
\end{figure}

The implementation of third-body interactions appears in Fig.~\ref{fig:E_nonlinear_plot}. This was computed using the same method as in the previous section, except for calculating the value for the scalar field $\bar\sigma$ instead of scalar density $n_\sigma$ when testing for different values of effective chemical potential. This also showed quick convergence. 
