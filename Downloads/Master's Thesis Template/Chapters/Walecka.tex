\section{Angle-resolved Photoemission Spectroscopy (ARPES)}
In our study we utilize angle-resolved photoemission spectroscopy (ARPES) to probe the electronic band structure of PtTe$_2$ and its Cr alloys. Analysis is then peformed using custom developed Python code. In this chapter, the physics of ARPES are reviewed, additionally the experimental setup at Lawrence Berkeley National Laboratory's (LBNL) Advanced Light Source (ALS) Beamline 4.0.3 MERLIN is outlined and discussed. The overarching analysis routines are additionally explored with specialized analysis routines being outlined in later chapters.
\subsection{The Physics of ARPES}
ARPES relies on the photoelectric effect, which was the subject of <PERSON>'s 1921 Nobel Prize in Physics. Additionally, quantum mechanical results in the form of <PERSON><PERSON><PERSON>'s Golden Rule are necessary to understand the intensity profile of the emitted electrons with is the form of our ultimate experimental data. 
\subsubsection{The Photoelectric Effect}
The photoelectric effect is the emission of electrons from a material when it is exposed to electromagnetic radiation. ARPES makes use of the photoelectric effect by utilizing electromagnetic radiation in the form of high-energy photons incident on a material. The photon transfers its energy to an electron in the material, which is then emitted as the nominal photoelectron. The energy of the emitted electron is measured, as well as the momentum via the emission angle. 
\\
The photoelectric effect can be outlined by the following equation
\begin{equation}
    h\nu = E_k + \phi + E_B
\end{equation}
where $h\nu$ is the energy of the incident photon, $E_k$ is the kinetic energy of the emitted electron, $\phi$ is the work function of the material, and $E_B$ is the binding energy of the electron in the material. The work function is the minimum energy required to remove an electron from the material, while the binding energy is the energy required to remove the electron from its band. 
The ARPES technique is primarily concerned with determining the binding energy of an emitted electron by measurement of the kinetic energy of emitted electrons with knowledge of the work function and the energy of the incident photon
$$E_B = h\nu - E_k - \phi$$
The binding energy is related to the Fermi level by the following equation
\begin{equation}
    E_B = E_F - E_k
\end{equation}
where $E_F$ is the Fermi level. Energy measurement only accounts for half of the ARPES technique, the other half being the measurement of the momentum of the emitted electron. The momentum of the emitted electron is measured by the emission angle. First we may convert energy to the magnitude of the momentum of the emitted electron using the following equation
\begin{equation}
    \left|\vec{p}_f\right| = \hslash \left|\vec{k}_f\right| = \sqrt{2m_eE_k} 
\end{equation}
where $m_e$ is the mass of the electron and $\left|\vec{k}_f\right|$ is the magnitude of the final wavevector of the emitted electron. Our incident photon transfers momentum only in the direction normal to the surface of the material, hence any parallel momentum the emitted electron has must come from its state in the crystal lattice. The conservation of momentum in the plane of the surface can be written as
\begin{equation}
    \vec{p}_{i\parallel} = \vec{p}_{f\parallel} \quad \Rightarrow \quad \left|\vec{p}_{i\parallel}\right| = \hslash \left|\vec{k}_{i\parallel}\right| = \sqrt{2m_e E_k}\sin\theta_e
\end{equation}
where $\theta_e$ is the emission angle. 
We can solve for the initial perpendicular component in terms of the kinetic energy $E_k$, the emission angle $\theta_e$ and the inner potential $V_0$ to arrive at the following expression
\begin{equation}
    \left|\vec{p}_{i\perp}\right| = \hslash \left|\vec{k}_{i\perp}\right| = \sqrt{2m_e E_k\left(\cos^2{\theta_e} +\frac{V_0}{E_k}\right)}
\end{equation}
For low-energy photon experiments, the momentum of the incoming photon is neglected, and the inner potential $V_0$ is determined by comparing photon-dependent scans.
Our study is interested in dispersion relations of the form $E(\vec{k})$, hence our necessary set of ARPES equations are as follows
\begin{equation}
    E_B = h\nu - E_k - \phi
\end{equation}
\begin{equation}
    \left|\vec{k}_{i\parallel}\right| = \frac{1}{\hslash}\sqrt{2m_e E_k}\sin\theta_e
\end{equation}
\begin{equation}
    \left|\vec{k}_{i\perp}\right| = \frac{1}{\hslash}\sqrt{2m_e E_k\left(\cos^2{\theta_e} +\frac{V_0}{E_k}\right)}
\end{equation}
By use of the photoelectric effect, and momentum conservation laws, the ARPES technique is able to extract the binding energy and momentum of the emitted electron via measurements of its emission angle $\theta_e$ and kinetic energy $E_k$. Interpretation of ARPES data in the context of quantum materials requires further inquiry. 
\subsubsection{Intensity Profile and Fermi's Golden Rule}
A full ARPES experiment involves the detection of high numbers of photoelectrons which builds out an intensity profile $I(\theta_e, E_k)$ from which the binding energy and momentum of the emitted electron can be extracted. Useful physical information is then extracted in forms of intensity profiles. The first of which is the energy-momentum profile $I(E, \left|\vec{k}_\parallel\right|)$ and the constant energy cut profile $I(k_x, k_y)\Big|_{E=const}$ which are acquired by transforming the intensity profile $I(\theta_e, E_k)$ via the previously mentioned conservation laws and geometric laws relating to the orientation of the measured sample. The measured intensity profile is related to the quantum mechanical electron states via the following relation
\begin{equation}
    I \propto \left|\left<\psi_f\left|\vec{A}\left(\omega\right)\cdot \vec{j}\right|\psi_i\right>\right|^2
\end{equation}
where $\psi_f$ and $\psi_i$ are the final and initial quantum mechanical states of the emitted electron, $\vec{A}\left(\omega\right)$ is the vector potential of the incoming photon, and $\vec{j}$ is the electric current density. More rigorously, one can utilize the Fermi's Golden Rule to arrive at the exact expression.
High resolution data extracted via the ARPES technique requires a consistent source of high-energy photons, which is provided by the ALS at LBNL.
\subsection{The Advanced Light Source (ALS) Beamline 4.0.3 MERLIN}
\subsection{Analysis Suite}

