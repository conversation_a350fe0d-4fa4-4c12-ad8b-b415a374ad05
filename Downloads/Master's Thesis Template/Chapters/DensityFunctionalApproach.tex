\section{Density Functional Approach for Isospin symmetric nuclear matter}
One of the main goals of using mean-field theories is to abandon the perspective of fields and instead switch to a perspective of densities. With this in mind, we develop a Density Functional approach~\cite{<PERSON><PERSON><PERSON><PERSON>} to obtain the same results as field theory does, but avoiding the procedure of dealing with an initial model. This is the biggest advantage of the formalism, we avoid starting from a Lagrangian.

Our first assumption is that if there were a Lagrangian $\mathcal{L}$ at all, it could be split into a non-interacting part $\mathcal{L}_0$ and an interacting part $\mathcal{L}_I$ such that $\mathcal{L}=\mathcal{L}_0+\mathcal{L}_I$. Evaluating through a partition function $\mathcal Z$ using imaginary time $\tau$, inverse temperature $\beta$, space $x$, and fields $\phi$ we find
\begin{equation}
\begin{split}
    \mathcal{Z}=&\int\prod_i[d\phi_i]\exp
\left(
\int_0^{\beta}d\tau\int{d}^3x~\mathcal{L}.
\right)\\
=&\int\prod_i[d\phi_i]\exp
\left(
\int_0^{\beta}d\tau\int{d}^3x~(\mathcal{L}_0+\mathcal L_I)
\right)
\end{split}~~,
\end{equation}
which can be used to calculate the grand potential $\Omega$
\begin{equation}
    \Omega=-T\ln\mathcal Z=\Omega_0+\Omega_I~~,
\end{equation}
which splits into non-interacting part and interacting part respectively. The pressure can be calculated using 
\begin{equation}
    P=-\frac{\Omega}{V}=-\frac{\Omega_0}{V}-\frac{\Omega_I}{V}=P_0+P_I
\end{equation}
which also splits into the non-interacting part $P_0$ and the interacting part $P_I$. This splitting is crucial for the formalism as it allows us to differenciate which quantities can be used to evaluate each term of the pressure. We first recognize that the pressure $P$ which will depend on the thermodynamic quantities of the ensemble, which are chemical potential $\mu$, mass $m$, and temperature $T$. The non-interacting pressure will be evaluated as a standard fermion gas produced by the particles themselves, which may (will) have shifted quantities due to the interactions. The shifted quantities are effective chemical potential $\mu^*$, effective mass $m^*$, {\color{red}Shifted temperature?} and also the system temperature $T$. The interacting pressure $P_I$ will depend on the mean-fields that would cause those interactions, which we will also use $\bar\sigma$, and $\bar\omega$. The pressure is then
\begin{equation}
    P(\mu, m, T)=P_0(\mu^*, m^*, T)+P_I(\bar\sigma,\bar\omega)~.
    \label{pressure_split}
\end{equation}

{\color{red} Where do the gap equations come from? Written in Extra}
Our next assumption is that we are dealing with minimal coupling. This assumption makes use of the effective quantities we just created to have gap equations of the form 
\begin{equation}
    \begin{split}
        m^*&=m-g_\sigma \bar\sigma\\
        \mu^*&=\mu-g_\omega \bar\omega        
    \end{split}
    \label{gap_0}
\end{equation}
as we had before. Here, we also choose the fields that interact with our system. The effective quantities $\mu^*$ and $m^*$ are not thermodynamic degrees of freedom, they are functions of chemical potential and mass respectively. 

The transformation we're looking to make is to evaluate the pressure from a more convenient quantity, and as said before, densities are quantities often used in QCD. We choose to use the same densities as before, which are
\begin{equation}
    \begin{split}
        n_s &\equiv -\frac{d P}{d m}=-\frac{\partial P_0}{\partial m^*}\\
        n_v &\equiv \frac{d P}{d \mu}=\frac{\partial P_0}{\partial\mu^*}        
    \end{split}
    \label{densities_definition}
\end{equation}
where $n_s$ is the scalar density and $n_v$ is the vector density. Note that these densities are known definitions and not unique to the Walecka model, for example, the multiple-body interaction model from Eq.~\eqref{multi_body_L} conserved the scalar density definition when adding more interaction terms. 

To find a relationship between the new densities and the fields, we can take the mean-field conditions, which are
\begin{equation}
    \begin{split}
        0=&\frac{dP}{d\bar\sigma}=\frac{\partial P_0}{\partial m^*}\frac{\partial m^*}{\partial \bar\sigma}+\frac{\partial P_I}{\partial \bar\sigma}=g_\sigma n_s+\frac{\partial P_I}{\partial \bar\sigma}\quad\rightarrow\quad g_\sigma n_s=-\frac{\partial P_I}{\partial \bar\sigma}\\
        0=&\frac{dP}{d\omega}=\frac{\partial P_0}{\partial \mu^*}\frac{\partial \mu^*}{\partial \omega}+\frac{\partial P_I}{\partial \omega}=-g_\omega n_v+\frac{\partial P_I}{\partial \omega}\quad\rightarrow\quad g_\omega n_v=+\frac{\partial P_I}{\partial \omega}
    \end{split}
\end{equation}
Using these definitions, we use a Legendre transformation on the interaction pressure $P_I$ we can get rid of the field dependencies, switching to the densities $n_\sigma$, $n_\omega$  instead of the fields themselves 
\begin{equation}
    P_I(\omega, \bar\sigma)=  - g_\sigma\bar\sigma n_s+ g_\omega\omega n_v-\tilde P_I(n_\sigma, n_\omega)~~,
    \label{Legendre_0}
\end{equation}
where $\tilde P_I$ is our new interaction pressure. This transformation implies the following relationships:
\begin{equation}
\begin{split}
    g_\sigma \bar\sigma=-\frac{\partial\tilde P_I(n_s, n_v)}{\partial n_s}\\
    g_\omega \omega=+\frac{\partial\tilde P_I(n_s, n_v)}{\partial n_v}
\end{split}
    \label{partial_fields}
\end{equation}
These relationships can be used in our gap equations from Eq.~\eqref{gap_0} to get
\begin{equation}
    \begin{split}
    m^*&=m+\frac{\partial\tilde P_I(n_s, n_v)}{\partial n_s}\\
        \mu^*&=\mu-\frac{\partial\tilde P_I(n_s, n_v)}{\partial n_v}
    \end{split}~~,
    \label{gap_1}
\end{equation}
and also in our pressure equation we got from the Legendre transformation in Eq.~\eqref{Legendre_0} to get
\begin{equation}
    P_I(\omega, \bar\sigma)=\frac{\partial\tilde P_I(n_s, n_v)}{\partial n_s}n_s+\frac{\partial\tilde P_I(n_s, n_v)}{\partial n_v} n_v-\tilde P_I(n_s, n_v) ~~.
    \label{Legendre_1}
\end{equation}
which can be rewritten and plugged back into our pressure definition from Eq.~\eqref{pressure_split} to get our final form
\begin{equation}
    P(\mu, m, T)=P_0(\mu^*, m^*, T)+\left(n_s\frac{\partial}{\partial n_s}+n_v\frac{\partial}{\partial n_v} -1\right)\tilde P_I(n_s, n_v)~.
    \label{pressure_split_0}
\end{equation}
which only depends on our choice of interaction pressure $\tilde P_I(n_s, n_v)$ to model the behavior of our system. 

One more advantage of this approach is that the formalism can be generalized to an N-flavor system, where there are $N$ particles and each particle quantity can be grouped in a vector such that the densities become $n_v\rightarrow\vec n_v$ and $n_s\rightarrow \vec n_s$. If we repeat the same process for a vector of densities, the pressure becomes   
\begin{equation}
    P(\vec \mu,\vec  m, T)=P_0(\vec\mu^*, \vec m^*, T)+\left(\vec n_s\cdot\vec\nabla_{\vec n_s}+\vec n_v\cdot\vec\nabla_{\vec n_v} -1\right)\tilde P_I(\vec n_s,\vec  n_v)~.
    \label{pressure_split_formalism}
\end{equation}
The non-interaction pressure becomes a sum of Fermion pressures caused by each individual type of particle as in 
\begin{equation}
    P_0(\vec\mu^*, \vec m^*, T)=\sum_iP_0(\mu_i^*,  m_i^*, T)~~,
\end{equation}
and each fermion-pressure contribution is 
\begin{equation}
    P_0=g T \int\frac{d^3p}{(2\pi)^3}\ln\left(e^{-\beta(E^*-\mu^*)}+1\right)
\label{p_FG_formalism}
\end{equation}
based on the definition in Eq.~\eqref{p_FG}. Note that antiparticles are also part of the particle vectors, where their effective chemical potential is $-\mu^*$. Here, the scalar and vector densities are functions of the effective quantities and temperature. The densities for each specific effective chemical potential $\mu^*_i$ and effective mass $m^*_i$ can be calculated using Eqs.~\eqref{densities_definition} and Eq.~\eqref{p_FG_formalism}, which are 
\begin{equation}
    \begin{split}
        n_s(\mu^*_i, m^*_i, T)=& g \int\frac{d^3p}{(2\pi)^3}\frac{m^*_i}{E^*_i} f_{FD}(E^*_i, \mu^*_i, T)
        \\
        n_v(\mu^*_i, m^*_i, T)=&g \int\frac{d^3p}{(2\pi)^3}f_{FD}(E^*_i, \mu^*_i, T)
    \end{split}
    \label{densities_integral}
\end{equation}
whereas before, $E_i^*=\sqrt{p^2+(m^*_i)^2}$, and the function $f_{FD}$ is the Fermi-Dirac distribution from Eq.~\eqref{Fermi_Dirac_Distribution}.

The gap equations then become 
\begin{equation}
    \begin{split}
    \vec m^*&=\vec m-\vec\Lambda\\
        \vec\mu^*&=\vec\mu-\vec\Omega
    \end{split}~~,
    \label{gap_formalism}
\end{equation}
where $\vec\Lambda=-\vec\nabla_{\vec n_s}\tilde P_I(\vec n_s,\vec  n_v)$ and $\vec\Omega=\vec\nabla_{\vec n_v}\tilde P_I(\vec n_s,\vec  n_v)$ are the interaction terms that we use to create our model.  The sign difference in the fields reflects attraction (in the scalar channel) and repulsion (in the vector channel).

\subsection{Recreating Walecka Model}

To recreate Lagrangian models such as the ones seen before, starting again from the field picture and transforming into the density picture, we can recreate the relationship between our densities and the fields. For example, a scalar density that reflects $N$ interaction terms would look like
\begin{equation}
    n_s=\sum^N_i a_i(g_\sigma\bar\sigma)^i
\end{equation}
where $a_i$ are the coefficients to fit the interactions. An example of this can be seen in Eq.~\eqref{multi_body_density}. Then, using Eq.~\eqref{partial_fields} we find that for the vector of scalar densities, 
\begin{equation}
    \vec n_s=\sum_i^N a_i \left(-\vec\nabla_{n_s} \tilde P(n_s, n_v)\right)^{\circ i}=\sum_i^N a_i \vec\Lambda^{\circ i}~~,
\end{equation}
where $^{\circ i}$ is the Hadamard power, which represents element-wise exponentiation. For a given set of $a_i$ coefficients, $\vec n_s$ can be solved for $\vec\Lambda(n_s)$.

To recreate the Walecka Model as we did previously, we can simply make linear interactions ($N=1$), which are equivalent to two-body interactions. We use the constants $G_\sigma$ and $G_\omega$ to regulate the interactions. Using $\vec\Lambda=-G_\sigma\vec n_s$ and $\vec \Omega=G_\omega \vec n_v$, we can calculate the interaction pressure using a Taylor series such that
\begin{equation}
    \begin{split}
        \tilde P_I(\vec n_s, \vec n_v)=&\tilde P_I(\vec 0,\vec 0)
        +\vec n_s\cdot\left.\vec\Lambda\right|_{n_s\rightarrow 0}
        + \vec n_v\cdot\left.\vec\Omega\right|_{n_v\rightarrow 0}\\
        &\qquad+\frac{1}{2}\vec n_s^{\circ 2}\cdot\left.(\vec\nabla_{\vec n_s}\circ\vec\Lambda)\right|_{n_s\rightarrow 0}+\frac{1}{2}\vec n_v^{\circ 2}\cdot\left.(\vec\nabla_{\vec n_v}\circ\vec\Omega)\right|_{n_v\rightarrow 0}~
        \dots\\
        =&-\frac{1}{2}G_\sigma \vec n_s^{2} +\frac{1}{2}G_\omega\vec n_v^{ 2}
    \end{split}
\end{equation}
where $\circ$ is the Hadamard product (element-wise multiplication), we use the notation $\vec a^2=\vec a\cdot\vec a$,  and $\tilde P_I(\vec 0,\vec 0)=0$ since there is no vacuum interaction pressure to consider. If there were a vacuum term to consider (as is common in QCD), this would be included in the non-interacting pressure $P_0$.

We can find the final pressure equation using Eq.~\eqref{pressure_split_formalism} to find 
\begin{equation}
    P(\vec\mu,\vec m, T) = P_0(\vec \mu^*, \vec m^*, T)-\frac{1}{2}G_\sigma \vec n_s^{2} +\frac{1}{2}G_\omega\vec n_v^{2}
\end{equation}
which is the vector equivalent of Eq.~\eqref{P_eq}. 

It's important to note that although Legendre transforms are often used in monotonic functions, if the function is non-monotonic, the resulting transformation is a multi-valued function. This is what we expect to happen at low temperatures to reflect the Liquid-gas phase transition. At low temperatures, the pressure is non-monotonic as a function of density, as seen in Fig.~\ref{fig:P_plot}, and its transformation as a function of chemical potential will result in a multivalued function, as seen in Fig.~\ref{fig:phase_transitions}.


\subsubsection{Generalized Densities of Vector Interactions}

A further advantage of this method is that we can now make a rotation in phase-space to transform from a frame of pure particle densities to a frame with generalized densities. As a first example, we only consider the vector channel interactions of the Walecka model in the new frame. This is, we ignore the scalar interactions, which is the same as setting $G_\sigma\rightarrow0$. We consider the Isospin symmetric system again, where protons and neutron densities are the same. We introduce our first generalized coordinate as the generalized baryon density $\rho_b$, which will be proportional to $n-\bar{n}$, which are the number of particles minus antiparticles. We also introduce the other generalized coordinate multiplicity density $\rho_\Omega$ which is proportional to the number of total particles and antiparticles  $n+\bar{n}$. In a compact notation:
\begin{equation}
\begin{pmatrix}
\rho_\Omega\\
\rho_b
\end{pmatrix}
=\frac{1}{\sqrt{2}}
\begin{pmatrix*}[r]
    1 & 1\\
    1 & -1
\end{pmatrix*}
\begin{pmatrix}
    n\\\bar{n}
\end{pmatrix}~,
\end{equation}
where the factor ensures that the matrix $M$ relating the generalized densities $\vec\rho=M\vec n$, will have the relation $M=M^{T}=M^{-1}$. It is important to note that because of the factor in the matrix, our generalized coordinate is not equal to the familiar baryon density we've been using, it is scaled. The relationship is $n_\omega=\sqrt{2}\rho_b$, which we must take into account when calculating the final quantities. 

Since the rotation is now from particle densities $\rightarrow$ generalized densities, we can repeat the same process as before when we did fields $\rightarrow$ densities. In this case, we redefine the interaction pressure quantities as $P_I\equiv P_I(\vec n)$ and $\tilde P_I\equiv \tilde P_I(\vec\rho)$, and the interaction terms $\Lambda$ and $\Omega$ now depend on the generalized densities from the new frame.

We can find a similar relationship between chemical potentials and their respective generalized quantities such that 
\begin{equation}
    \vec\mu\cdot\vec n=\vec\mu^T M^{-1}M\vec n=\vec\nu\cdot\vec\rho
\end{equation}
where we introduce the generalized chemical potentials $ \vec\nu=M^{-1,T}\vec\mu$, which can also be rewritten as $\vec\mu=M^{T}\vec\nu$. The advantage of using generalized chemical potentials is that it allows us to choose what quantities we want to conserve. For instance, if we don't want to conserve the particle number in the system, we would set $\nu_\Omega=0$, so that the total number of particles and antiparticles can vary. The same would go for $\nu_b$ if the baryon density was not conserved. In our case, only the baryon density will be conserved. 

Using this assumption:
\begin{equation}
    \begin{pmatrix}
        \mu_n\\ 
        \mu_{\bar n}
    \end{pmatrix}
    =M^T
\begin{pmatrix}
    \nu_\Omega\\ \nu_b
\end{pmatrix}= \frac{1}{\sqrt{2}}
\begin{pmatrix*}[r]
    1 & 1\\
    1 & -1
\end{pmatrix*}
\begin{pmatrix}
    0 \\ \nu_b
\end{pmatrix}~,
\end{equation}
which has the solution $\nu_b=\sqrt{2}~\mu_n$ with $\mu_n=-\mu_{\bar n}$.

As we've done before, this also applies to the effective chemical potential quantities too:
\begin{equation}
    \vec\nu^*=M^{-1,T}\vec\mu^*\text{, and } \vec\mu^*=M^{T}\vec\nu^*.
    \label{effective_potentials}
\end{equation}
Then, $\nu^*$ is calculated using the mean-field approximation $\vec\nu^*=\vec\nu-\vec\Omega(\vec\rho)=\vec\nu-\vec\nabla_{\vec\rho}\tilde P_I(\vec\rho)$ where $\tilde P$ is the interaction pressure in terms of the generalized coordinates. 

In the Walecka model, we assume the interaction pressures to be of the form $\vec\Omega(\vec\rho)=\vec\nabla_{\vec\rho}\tilde P_I(\vec\rho)= G_{\omega}^i\rho_i$, where $G_\omega^i$ is a vector in the new frame, where the \textit{i}th components is the coupling parameter of its respective generalized density. The subscript $_\omega$ just symbolizes that they are related to the vector density coupling in the original frame.

\begin{figure}
    \centering
    \includegraphics[width=0.75\linewidth]{Figures/ZeroTempNoEffectiveMass.png}
    \caption{Pressure using no scalar field to test the density functional approach using a relaxation method with parameter $\gamma=0.05$ and ranging $\nu$ to the values $1200-3000~\text{MeV}$}
    \label{fig:zeroTempNoMass}
\end{figure}

To run a quick test on this, we can run a simulation in the zero-temperature limit as we did before in the Walecka model section. The reason for the zero-temperature limit is purely computational, as will be explained later. In this case, we set $m^*=m_N=939~\text{MeV}$ since there are no scalar interactions, and run a simulation doing all the operations in the generalized space instead of particle space. Since there is no scalar interaction, there is no particle attraction in our system, which will make the pressure always positive. 

Solving for the generalized effective chemical potentials and using Walecka type densities, we find
\begin{equation}
    \begin{pmatrix}
        \nu^*_\Omega\\ 
        \nu^*_b
    \end{pmatrix}=\vec\nu-\vec\Omega(\vec\rho)=
    \begin{pmatrix}
    -G_\omega^\Omega\rho_\Omega \\ 
    \nu_b-G_\omega^b\rho_b
    \end{pmatrix}~,
\end{equation}
which we can use to find solutions for $\vec\nu^*$ in terms of $\nu_b$. We can run multiple generalized chemical potential values and use a similar process as the previous simulations to find solutions. 

It's important to note that the only densities we are able to calculate here are the particle densities using Eq.~\eqref{densities_integral}, since the generalized densities may not obey Fermi-Dirac statistics. However, changing from one frame to the other is easy using the matrix $M$, which makes us able to calculate the generalized densities $\vec\rho$.

To determine the coupling constants $G$ we can compare the interaction pressure from both frames since one of them is already known at the zero temperature limit. Since at this limit $\bar n\rightarrow0$, then the only contribution interaction contribution in the pressure from the particle density frame is that of the particles and not of the antiparticles. 
\begin{equation}
    \begin{split}
         P_I=\frac{1}{2}G_\omega n^{2}&=\frac{1}{2}G_\omega^\Omega\rho_\Omega^{2}+\frac{1}{2}G_\omega^b\rho_b^{2}\\
         \frac{1}{2}G_\omega n^{2}&=\frac{1}{4}G_\omega^\Omega n^{2}+\frac{1}{4}G_\omega^b n^{2}\\
         &\downarrow\\
         G_\omega&=\frac{1}{2}(G_\omega^\Omega + G_\omega^b)
    \end{split}
\end{equation}
which gives us a new degree of freedom to choose how much to couple the multiplicity channel vs. the baryon channel. We define a parameter $\alpha$ to vary between the two
\begin{equation}
    \begin{split}
        G_\omega^b &= (1 - \alpha) ~2  G_\omega \\        
        G_\omega^\Omega &= \alpha~  2  G_\omega\\
    \end{split}~~,
\end{equation}
which will determine how much to "rotate" in phase space. Defined this way, when $\alpha\rightarrow0$ there is no change in the densities at all since multiplicity disappears and we only look at baryon density, which is the standard Walecka picture we have seen so far. As a reminder, the constant also relates to the Yukawa coupling constant of the $\omega$ field and its mass via $G_\omega = (g_\omega/m_\omega)^2$.

As done previously to calculate quantities numerically, we used a relaxation method, which is of the form 
\begin{equation}
    \vec\nu^*\rightarrow\gamma(\vec\nu-\vec\Omega(\vec\rho))+(1-\gamma)\vec\nu^*
\end{equation}
where $\gamma$ is the relaxation parameter and this is done in a loop until convergence. 

The result of this can be seen in Fig.~\ref{fig:zeroTempNoMass}, where the pressure is always positive as expected.

\subsubsection{Adding a Scalar interaction}

\begin{figure}
    \centering
    \includegraphics[width=0.75\linewidth]{Figures/ZeroTemp.png}
    \caption{Pressure to test the density functional approach using a relaxation method with parameters $\gamma=0.05$ and $\delta=0.005$, ranging $\nu$ to the values $1300-1700~\text{MeV}${\color{red}Lower axis might not be scaled correctly, might have to redo}}
    \label{fig:ZeroTemp}
\end{figure}

Now incorporating the scalar field into the isospin symmetric model, we define our generalized scalar densities $\vec\eta$ as
\begin{equation}
    \begin{pmatrix}
        \eta_\Omega \\ \eta_b
    \end{pmatrix}
    =M
    \begin{pmatrix}
        n_\sigma \\ \bar n_\sigma
    \end{pmatrix}
\end{equation}
and generalized mass $\vec\zeta$ as
\begin{equation}
    \begin{pmatrix}
        \zeta_\Omega \\ \zeta_b
    \end{pmatrix}
    =M^{-1,T}
    \begin{pmatrix}
        m_n \\ m_{\bar n}
    \end{pmatrix}
\end{equation}
the same way we did for vector densities and chemical potentials. In our case we know $m_n=m_{\bar n}$, which makes the baryon mass $\zeta_b=0$. The multiplicity mass results in $\zeta_\Omega=\sqrt{2}~m_n$. Using the same procedure, and setting $\vec\Lambda(\vec \eta)=-\vec\nabla_{\vec\eta}\tilde P(\vec\eta)$ from Eq.~\eqref{gap_formalism} we find our gap equation
\begin{equation}
    \begin{pmatrix}
        \zeta^*_\Omega\\ 
        \zeta^*_b
    \end{pmatrix}=\vec\zeta-\vec\Lambda(\vec \eta)=
    \begin{pmatrix}
    \zeta_\Omega+G_\sigma^\Omega\eta_\Omega \\ 
    G_\sigma^b\eta_b
    \end{pmatrix}~,
\end{equation}
where the subscript $_\sigma$ in the coupling parameters denote that they are related to the coupling of the scalar channel in the original picture. The coupling constants in this case are
\begin{equation}
    \begin{split}
        G_\sigma^b &= \beta ~2  G_\sigma\\
    G_\sigma^\Omega&= (1 - \beta)~  2G_\sigma
    \end{split}~~,
\end{equation}
which are defined that way so that when $\alpha=\beta=0$, the Standard Walecka model is reproduced.

We use another relaxation method as 
\begin{equation}
    \vec\zeta^*\rightarrow\delta(\vec\zeta-\vec\Lambda)+(1-\delta)\vec\zeta^*
\end{equation}
where $\delta$ is another relaxation parameter. The result can be seen in Fig.~\ref{fig:ZeroTemp}, which gives us the same results as Fig.~\ref{zeroTempPlots} as expected. 

\subsubsection{Finite-Temperature Limit}

However, as our gap equations get more complicated, the relaxation method used so far starts to become inefficient, and struggles to find solutions for specific ranges. A more stable and efficient method is required. From now on, we will use Python's \textbf{scipy} library routines that automate this process, ensuring that the most stable and efficient method is applied. 

We use \textbf{scipy.optimize.root} to find the roots or solutions to or coupled equation system. We create a function that will return the gap equations in the form 
\begin{equation}
\begin{split}
    \vec\nu^*-\vec\nu+\vec\Omega(\vec\rho)=0 \\
    \vec\zeta^*-\vec\zeta+\vec\Lambda(\vec\eta)=0
\end{split}
\end{equation}
to feed into \textbf{root()} with an initial guess to calculate the Jacobian to determine the best method to use. 

\begin{figure}
\begin{subfigure}{.5\textwidth}
  \centering
  \includegraphics[width=\linewidth]{Figures/mass_chemicalPotentials.png}
  \caption{}
  \label{fig:mass_chemicalPotentials}
\end{subfigure}%
\begin{subfigure}{.5\textwidth}
  \centering
  \includegraphics[width=\linewidth]{Figures/mass_chemicalPotentials_Zoom.png}
  \caption{}
  \label{fig:mass_chemicalPotentials_Zoom}
\end{subfigure} \\
    \caption{Effective mass, effective chemical potential, and chemical potential as a function of baryon density.}
    \label{fig:chem_potential}
\end{figure}


However, it is important to note that because of the liquid-gas phase transition as seen in section \ref{Liquid_gas_phase}, different types of matter can exist at low temperatures before the Liquid-Gas phase transition happens. It is common to use the chemical potential $\mu$ of the system as the variational parameter of the simulation as it is one of the thermodynamic parameters. Varying $\mu$ only once may only give one of the possible solutions, this is because baryon density is a multivalued function of $\mu$. This can be seen in Fig.~\ref{fig:chem_potential}, where there is a region (before the phase transition happens) with three solutions. This tells us that we must sweep through that range of chemical potentials at least 3 times with the goal of the algorithm to converge toward all the solutions. We do this by starting with different initial guesses at the effective quantities for the \textbf{root} algorithm to find all solutions. This part is often done by trial and error since some solutions are more stable than others. 

\begin{figure}
\begin{subfigure}{.33\textwidth}
  \centering
  \includegraphics[width=\linewidth]{Figures/mu_plot_0.png}
  \caption{}
\end{subfigure}%
\begin{subfigure}{.33\textwidth}
  \centering
  \includegraphics[width=\linewidth]{Figures/mu_plot_2.png}
  \caption{}
\end{subfigure}
\begin{subfigure}{.33\textwidth}
  \centering
  \includegraphics[width=\linewidth]{Figures/mu_plot_8.png}
  \caption{}
\end{subfigure}\\
    \caption{Effective masses, effective chemical potentials, and chemical potential as a function of baryon density for different shifts between the multiplicity and baryon coupling constants. This shows that the values are very different.}
    \label{fig:gen_chem_potential}
\end{figure}

When running the zero temperature limit for different $\alpha$ and $\beta$ parameters, we find the results in Fig.~\ref{fig:gen_chem_potential}, where the quantities can evolve very differently depending on the parameters used. In most cases, the effective chemical potentials and the effective masses do not share a symmetry between them, which is not something commonly seen in Field Theory. However, the relationship between the chemical potential and the baryon density is the same. 

\begin{figure}
    \begin{subfigure}{0.5\textwidth}
        \centering
        \includegraphics[width=\linewidth]{Figures/mu_vs_P_2.png}
    \end{subfigure}
    \begin{subfigure}{0.5\textwidth}
        \centering
        \includegraphics[width=\linewidth]{Figures/mu_vs_P_4.png}
    \end{subfigure}
    \caption{Liquid-gas phase transition as seen by how the pressure changes with baryon density and chemical potential. Notice how the parameters $\alpha$ and $\beta$ do not change the result as pressure is thermodynamically consistent in the multiplicity picture with chemical potential.}
    \label{fig:pressure_phase}
\end{figure}

In fact, when testing thermodynamic quantities such as pressure, we find that the parameters used do not change their result. This can be seen in Fig.\ref{fig:pressure_phase} where the phase transition happens between $15-20~\text{MeV}$. This is expected as the theory is thermodynamically consistent. The effective quantities are the ones that change accordingly with the parameters used.


\subsubsection{Quantities under Formalism}
{\color{red}Show dressed mass, chemical potential and other quantities under the formalism}


\begin{figure}
    \begin{subfigure}{0.32\textwidth}
        \centering
        \includegraphics[width=\linewidth]{Figures/eff_mu_full_1.png}
    \end{subfigure}
    \begin{subfigure}{0.32\textwidth}
        \centering
        \includegraphics[width=\linewidth]{Figures/eff_mu_full_0.png}
    \end{subfigure}
    \begin{subfigure}{0.32\textwidth}
        \centering
        \includegraphics[width=\linewidth]{Figures/eff_mu_full_-1.png}
    \end{subfigure}
    \caption{Antiparticles may appear at zero temp! {\color{red}Rewrite}}
    \label{fig:antiparticlesZeroTemp}
\end{figure}


\subsection{Comparison to Excluded Volume Approach}
\begin{figure}
    \begin{subfigure}{0.5\textwidth}
        \centering
        \includegraphics[width=\linewidth]{Figures/ExcludedVolumeComparison.png}
    \end{subfigure}
    \begin{subfigure}{0.5\textwidth}
        \centering
        \includegraphics[width=\linewidth]{Figures/ExcludedVolumeComparison2.png}
    \end{subfigure}
    \caption{Excluded volume using $r=0.5~\text{fm}$.}
    \label{fig:excludedvolumeFig}
\end{figure}

The excluded volume approach~\cite{excludedVolume} involves
\begin{equation}
    \mu^* = \mu - V_{\text{ex}}P_0(\mu^*, T)
\end{equation} 
and no dressed mass or pressure interaction term. The baryon density is defined as 
\begin{equation}
    n_{\text{ex}}=\frac{n_v}{1+V_{\text{ex}}n_v}
\end{equation}
where $V_{\text{ex}}=4\pi r^3/3$. 

As we can see in Fig.~\ref{fig:excludedvolumeFig}, the Trace Anomaly of the excluded volume and ideal gas can be recreated by tuning the $\alpha$ and $\beta$ parameters to be about 0.45. And at higher temperatures (beyond the deconfinement phase) they do not diverge as the excluded volume does. 

{\color{red} Add math to show excluded volume divergence and show how our approach is better.}

\subsection{Isospin Asymmetric Nuclear Matter}
{\color{red}Check Klaehn2008 article}
Following the same procedure, we can extend this to asymmetric isospin, that is, consider protons $p$ and neutrons $n$ separately. However, to include isospin, there is a third meson field that the baryons couple to. This field is the $\rho$ meson field. This does not change the gap equations, and therefore won't change the matrix relationships. However, it does now add a term to the pressure since there is a new density on which the pressure depends. We will see this below.

We set our matrix $M$ as:

\begin{equation}
\vec{\rho}=
\begin{pmatrix}
\rho_\Omega\\
\rho_b\\
\rho_{\bar{\Omega}}\\
\rho_I
\end{pmatrix}
=\frac{1}{2}
\begin{pmatrix*}[r]
    1 & 1 & 1 & 1\\
    1 & -1 & 1 &-1\\
    1 & 1 & -1 & -1\\
    1 & -1 & -1 & 1
\end{pmatrix*}
\begin{pmatrix}
    n\\\bar{n}\\p\\\bar{p}
\end{pmatrix}~,
\end{equation}
where $\rho_I$ is the isospin density and $\rho_{\bar\Omega}$ is the isospin multiplicity density. Notice that from the isospin symmetric case, we calculated the matrix by doing the operation $M \otimes M$. {\color{red} Expand on meaning of this.}

Now the generalized chemical potentials will be the same as before, so $\vec{\nu}=M^{-1,T}\vec{\mu}$. Our constraint is that $\nu_\Omega=\nu_{\bar{\Omega}}=0$, where the isospin multiplicity generalized chemical potential $\nu_{\bar{\Omega}}$ is set to zero to allow different proton and neutron densities (and their antiparticle densities). This was not the case in the isospin symmetric case. With this constraint we find 
\begin{equation}
    \begin{split}
        \mu_n=&-\mu_{\bar{n}}\\
        \mu_p=&-\mu_{\bar{p}}\\
        \nu_I=&~\mu_n-\mu_p\\
        \nu_b=&~2\mu_p+\nu_I
    \end{split}
\end{equation}
where the isospin symmetric case can be recovered if we set $\nu_I\rightarrow 0$. We calculate the effective chemical potentials using  $\vec\nu^*=\vec\nu-\vec\nabla_{\vec\rho}\tilde P(\vec\rho)$, where using a Walecka type model will generate the new constants $G_{\bar{\Omega}}$ and $G_I$ besides the constants defined in the isospin symmetric case.

Repeating the process with the scalar field, we find 
\begin{equation}
    \vec{\eta}=
    \begin{pmatrix}
    \eta_\Omega\\
    \eta_b\\
    \eta_{\bar{\Omega}}\\
    \eta_I
    \end{pmatrix}
    =\frac{1}{2}
    \begin{pmatrix*}[r]
        1 & 1 & 1 & 1\\
        1 & -1 & 1 &-1\\
        1 & 1 & -1 & -1\\
        1 & -1 & -1 & 1
    \end{pmatrix*}
    \begin{pmatrix}
n_\sigma\\\bar{n}_\sigma\\p_\sigma\\\bar{p}_\sigma
    \end{pmatrix}~,
    \end{equation}
where $\eta_I$ is the isospin density and $\eta_{\bar{\Omega}}$ is the isospin multiplicity density. The generalized masses in this case are more trivial since we know $m_N=m_{\bar N}$ and $m_P=m_{\bar P}$. This makes $\zeta_b=\zeta_I=0$, and $\zeta_{\bar\Omega}=m_N-m_P$ and $\zeta_{\bar\Omega}=m_N+m_P$, which we know the values of since $m_N\approx 939~\text{MeV}$ and $m_P\approx 938~\text{MeV}$.

{\color{red}This next part is incorrect, there is a third field that couples to get isospin}
To determine the coupling constants, we do an additional Legendre transform around $n_\omega$ of the Pressure as done in the formalism~\cite{Klaehn}, and find
\begin{equation}
\begin{split}
    P(\mu, m, T) = P_0(\mu^*, m^*, T) + &\frac{1}{2}G_\omega n_\omega^2 + \frac{1}{2}G_\sigma n_\sigma^2 +\frac{1}{2}G_\rho n_\rho^2=\\
   = P_0(\mu^*, m^*, T) +& \frac{1}{2}G_\Omega n_\Omega^2 + \frac{1}{2}G_b n_b^2 \\
   + &\frac{1}{2}G_{\bar\Omega} n_{\bar\Omega}^2 + \frac{1}{2}G_I n_I^2 +  \frac{1}{2}G_\sigma n_\sigma^2
\end{split}~~,
\end{equation}
where $G_\rho$ is the general coupling constant for the new field, and $n_\rho$ is the isospin density.
We find here that 
\begin{equation}
    G_\omega n_\omega^2+G_\rho n_\rho^2 = G_\Omega n_\Omega^2 + G_b n_b^2  + G_{\bar\Omega} n_{\bar\Omega}^2 + G_I n_I^2 ~~.
\end{equation}
At the $T\rightarrow 0$ limit, the proton and neutron antiparticle densities go to 0, so this simplifies to
\begin{equation}
    G_\omega (n + p)^2+G_\rho (n-p)^2 = \frac{1}{4}(G_\Omega + G_b)(n + p)^2 + \frac{1}{4}(G_{\bar\Omega} + G_I)(n-p)^2
\end{equation}
where we find that $G_\Omega + G_b=4G_\omega$ and $G_{\bar\Omega} +G_I=4G_\rho$. This process is repeated for the scalar field constants.

{\color{red}The main takeaway is:}
\begin{equation}
    P(\vec\mu,\vec m, T) = \sum_i\left(P_0(\mu_i^*, m_i^*, T) + n_\sigma^i\frac{\partial \tilde P_I(n_\sigma^i,n_\omega^i)}{\partial n_\sigma^i}+ n_\omega^i\frac{\partial \tilde P_I(n_\sigma^i, ,n_\omega^i)}{\partial n_\omega^i}  -P_I( n_\sigma^i, n_\omega^i)\right) 
\end{equation}
where $i$ sums over each particle to consider. By doing the Taylor expansion up to $n=2$ we get our final expression for pressure to be
\begin{equation}
    P(\vec\mu,\vec m, T) = \sum_i\left(P_0(\mu_i^*, m_i^*, T) -\frac{1}{2}G_\sigma(n_\sigma^i)^2+ \frac{1}{2}G_\omega(n_\omega^i)^2\right) 
\end{equation}
\clearpage
\section{Speed of Sound}

{\color{red}Check Cern2 article for info and context, as well as Roberts and Arzate.}

{\color{red} Add reference to why this is the definition. Wikipedia}
The speed of sound is defined as 
\begin{equation}
    c_s^2=\frac{d P}{d \epsilon}
\end{equation}
where $P$ is the system pressure and $\epsilon$ is the binding energy. 

We can expand this equation by 
\begin{equation}
    c_s^2=\frac{dP}{d\epsilon}=\frac{dP}{d\epsilon_0} \frac{d\epsilon_0}{d\epsilon}
\end{equation}
where $\epsilon_0$ is the non-interacting part of the binding energy. Then
\begin{equation}
    \frac{dP}{d\epsilon_0}=\frac{dP_0}{d\epsilon_{0}}+\frac{dP_I}{d\epsilon_{0}}=c^2_{s_{0}}+\frac{dP_I}{d\epsilon_{0}}
\end{equation}
where $c^2_{s_{0}}$ is the speed of sound of the non-interacting fermion gas. Also, consider
\begin{equation}
    \frac{d\epsilon_{0}}{d\epsilon}=\frac{1}{\frac{d\epsilon}{d\epsilon_{0}}}=\frac{1}{\frac{d\epsilon_0}{d\epsilon_{0}}+\frac{d\epsilon_I}{d\epsilon_{0}}}=\frac{1}{1+\frac{d\epsilon_I}{d\epsilon_{0}}}~~.
\end{equation}
Then using the expression for the non-interacting binding energy $\epsilon_0=-P_0+T S_0+\mu^*~n_v$, we find
\begin{equation}
    \frac{d}{d\epsilon_0}=\frac{d n_v}{d\epsilon_0}\frac{d}{d n_v}=\frac{1}{\mu^*}\frac{d}{d n_v}~~,
\end{equation}
which using the last few expressions, we find the speed of sound to be 
\begin{equation}
    c_s^2=\frac{c_{s_0}^2+\frac{1}{\mu^*}\frac{dP_I}{d n_v}}{1+\frac{1}{\mu^*}\frac{d\epsilon_I}{d n_v}}~~,
\end{equation}
which separates the speed of sound of a fermi gas from its interaction contributions.

{\color{red}
\begin{itemize}
    \item Expected values for ideal gas, nuclear and quark matter
\end{itemize}}

\subsection{High-Temperature limit}

At high-temperature limit, the Pressure goes to {\color{red}Derive}
\begin{equation}
    P\rightarrow \frac{g}{6\pi^2}\mu^4
\end{equation}
and the energy goes to 
\begin{equation}
    E\rightarrow\frac{3g}{6\pi^2}\mu^4
\end{equation}
which makes the speed of sound approach $c_s^2\rightarrow1/3$.


\subsection{High-density limit}

At high density, the vector field contribution dominates{\color{red}Derive}
\begin{equation}
    P\rightarrow \epsilon\rightarrow \frac{1}{2}m_\omega^2\bar\omega^2
\end{equation}
which makes the speed of sound approach the speed of light at the high-density limit.