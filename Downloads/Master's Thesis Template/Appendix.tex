\begin{center}
    \large\textbf{APPENDIX}\\  
    \vspace{0.2cm}
\end{center}
\addcontentsline{toc}{section}{Appendix}
\renewcommand{\theequation}{A.\arabic{equation}}

%\section{Appendix}
\section*{Python Implementation}
{\color{blue} Currently commented out}

\section*{Gap Equations from Thermodynamic derivation}

If we define a pressure $P(n, T)$ which depends on the baryon density $n$ and temperature $T$ (as it would in a canonical ensemble), we find the relationship between chemical potential $\mu$ and pressure with 
\begin{equation}
    n=\frac{\partial P(\mu, T)}{\partial \mu}~~, 
\end{equation}
as seen before. We can make a Legendre transformation to the grand canonical ensemble to $\tilde P(n, T)$ by
\begin{equation}
   \tilde P(n, T) = \mu n - P(\mu, T)~~,
\end{equation}
which when taking a partial derivative gives the resulting relationship 
\begin{equation}
    \mu=\frac{\partial\tilde  P(n, T)}{\partial n}~~.
\end{equation}
It's important to note that $\tilde P$ is not pressure, but its a free energy that is related to pressure.

Knowing that $\tilde P$ can be split into the non-interacting part $\tilde P_0$ and interacting part $\tilde P_I$, we find that
\begin{equation}
    \mu=\frac{\partial\tilde P_0(n, T)}{\partial n} + \frac{\partial\tilde P_I(n, T)}{\partial n} = \mu^* + \Omega
\end{equation}
where $\mu^*$ is the effective chemical potential that appears in the fermion gas (the non-interacting part of the system), and $\Omega$ is the interaction term. With this, we find the gap equation used previously
\begin{equation}
    \mu^*=\mu-\Omega~~.
\end{equation}
The same can be done for the scalar gap equation as
\begin{equation}
    m=\frac{\partial\tilde P}{\partial n_s}=\frac{\partial \tilde P_0}{\partial n_s} + \frac{\partial P_I}{\partial n_s} = m^* + \Lambda~~,
\end{equation}
to get
\begin{equation}
    m^*=m-\Lambda
\end{equation}

\section*{Adding Quarks to the Model}
{\color{red} Robers and maybe Arzate}
When considering quarks in the model, the vacuum term in the Pressure in Eq.~\eqref{p_FG_1} cannot be ignored. {\color{red} Explain why}. The vacuum term will also appear in the scalar density $n_\sigma$ for quarks when being calculated according to Eq.~\eqref{fields_densities}. However, the vector density $n_\omega$ will remain the same since the vacuum term does not depend on chemical potential. The scalar density for quarks is then 
\begin{equation}
     n_\sigma^q=-\frac{\partial P_0(\mu^*,m^*,T)}{\partial m^*}=g_q \int\frac{d^3p}{(2\pi)^3}\frac{m^*}{E^*}\left( 1-f_{FD}(E^*, \mu^*, T)-\bar  f_{FD}(E^*, \mu^*, T)\right)~~,
\end{equation}
where $g_q$ is the quark degeneracy factor. Here, the vacuum term diverges as well as the vacuum term in the Pressure, so we will have to use a cutoff when evaluating. One more thing to consider is the fact the degeneracy factor $g$ is different for quarks and hadrons (which is why the distinction is now made). Hadron degeneracy has two particles (neutrons and protons) and two spin states, making its degeneracy factor $g_h=2\times2=4$. Quarks have two types of particles (we're only considering up and down), two spin states, and three color states (red, green, blue), making their degeneracy $g_q=2\times2\times3=12$.

Using the formalism, we can write out the total pressure relation. First, we look at the non-interacting fermion gas contributions
\begin{equation}
    P_0(\vec\mu^*, \vec m^*, T)=\sum_{\text{baryons}}P_0^b(\mu_i^*, m_i^*, T) + \sum_{\text{quarks}} P_0^q(\mu_i^*, m_i^*, T)
\end{equation}
where the superscripts $^b$ will from now on denote baryon and $^q$ quark quantities.

\section*{Pressure contribution of each field}

\begin{figure}
    \centering
    \includegraphics[width=0.8\linewidth]{Figures/Field contributions to pressure.png}
    \caption{Pressure contribution of scalar and vector field as a function of temperature density for constant chemical potential.}
    \label{fig:field_contributions}
\end{figure}

Another interesting quantity to check is the contribution of each field to the total pressure for $\mu\rightarrow0$ as the temperature increases. We can see this in Fig.~\ref{fig:field_contributions}, where the scalar field contribution to the pressure only appears for a small range of temperatures. This is due to the effective mass converging to a value at high temperatures. {\color{red} Expand}. The vector field contributions appear to increase as the temperature increases, but it turns out that this contribution is not significant for high temperatures, as it goes with $T^{1/4}$, whereas the total pressure increases as $T^{4}$.

\section*{Neutron Stars}
{\color{red} Arzate}

\section*{Scalar gap equation at high-temperature limit}

The scalar density is
\begin{equation}
    n_\sigma=g\int\frac{d^3p}{(2\pi)^3}\frac{m^*}{E^*}\left(\frac{1}{e^{(E^*-\mu^*)/T}+1}+\frac{1}{e^{(E^*+\mu^*)/T}+1}\right)
\end{equation}
Using spherical symmetry $d^3p=4\pi dp$ and a variable substitution $dE^*/dp=p/E^*$:
\begin{equation}
    n_\sigma=g\int_{m^*}^\infty\frac{dE^*}{2\pi^2} m^*\sqrt{(E^*)^2-(m^*)^2}\left(\frac{1}{e^{(E^*-\mu^*)/T}+1}+\frac{1}{e^{(E^*+\mu^*)/T}+1}\right)
\end{equation}

For high temperatures, we can transition to Boltzmann statistics where $e^{(E^*\pm\mu^*)/T}+1\rightarrow e^{(E^*\pm\mu^*)/T}$, the integral becomes 
\begin{equation}
    n_\sigma =\frac{g}{\pi^2}(m^*)^2T~K_1\left(\frac{m^*}{T}\right)\cosh\left(\frac{\mu^*}{T}\right)
\end{equation}

where $K$ is a hyperbolic Bessel function of the second type. Notice $K_1(m^*/T)\rightarrow T/m^*$ at high temperature. At the high temperature limit, $\cosh(\mu^*/T)\rightarrow1$. Then plugging this into the mass gap equation, we get 
\begin{equation}
    m^*=m-G_\sigma\frac{ g}{\pi^2}T^2m^*
\end{equation}
which we can solve for $m^*$ as
\begin{equation}
    m^*=\frac{m}{1+\tilde G_\sigma T^2}
    \label{mass_gap}
\end{equation}
where $\tilde G_\sigma = G_\sigma \dfrac{g}{\pi^2}$, which satisfies the limit $m^*\rightarrow m$ at zero temperature, and $m^*\rightarrow 0$ at high temperature.

{\color{blue} TK: For baryons that's probably a good result. For quarks we have to be more careful.
Define:
\begin{equation}
    n_\sigma=g\int\frac{d^3p}{(2\pi)^3}\frac{m^*}{E^*}\left(
    1
    -\frac{1}{e^{(E^*-\mu^*)/T}+1}
    -\frac{1}{e^{(E^*+\mu^*)/T}+1}
    \right)
\end{equation},
and adjust signs in the gap equation accordingly. The leading term is often considered as a vacuum term but it is easy to understand that with $m^*$ the vacuum changes, too.

A problem with the vacuum term is that it diverges. Now, one could regularize the integral by a cut-off scheme (there are many).
Alternatively, we can assume that the vacuum term is well defined:
\begin{equation}
n_\sigma^0=n_\sigma(T=0,\mu=0).
\end{equation}

Not worrying about multiplicities, here only the leading term - the condensate - will contribute.

We write
\begin{equation}
    \frac{m^*}{E^*}=\frac{M_0^*}{E_0^*}+\frac{m^*}{E^*}-\frac{M_0^*}{E_0^*},
\end{equation}
where $M_0^*$ and $E_0^*$ are the vacuum dressed mass and corresponding dispersion relation in vacuum at zero temperature.
In this cold vacuum, the second and third term cancel by definition.
Instead of worrying about how to regularize the condensate term of the scalar density in 
cold vacuum, we just assume {\it it can be done} and define
\begin{equation}
n_\sigma^0=
g\int_{\Lambda}\frac{d^3p}{(2\pi)^3}\frac{M^*}{E^*},
\end{equation}
which we identify as a number that follows after some regularization procedure $\Lambda$ has been performed.
The gap equation in cold vacuum thus reads
\begin{equation}
M_0^*=m+G_\sigma n_\sigma^0,
\end{equation}
viz.
\begin{equation}
n_\sigma^0=\frac{M_0^*-m}{G_\sigma}.
\end{equation}
It appears that we don't need to know the regularization scheme to determine the cold vacuum condensate as long as we know the dressed mass in cold vacuum $M_0^*$, and the bare mass $m$. $G_\sigma$ needs more data to be determined but clearly is a model parameter as all other couplings are in our approach.
Out of cold vacuum, we write
\begin{equation}
    n_\sigma=n_\sigma^0+g\int\frac{d^3p}{(2\pi)^3}
    \left[\frac{m^*}{E^*}
    -\frac{M_0^*}{E_0^*}
    -\frac{m^*}{E^*}\left(
    \frac{1}{e^{(E^*-\mu^*)/T}+1}
    +\frac{1}{e^{(E^*+\mu^*)/T}+1}
    \right)
    \right].
\end{equation}

The last two terms in this equation can be evaluated in the high-temperature limit, as before.

{\it The first two terms under the integral  (probably) still diverge (I guess logarithmically).
Daniel, can you check?}
}

\section*{Baryon density at high-temperature limit}
The baryon density is defined as 

\begin{equation}
    n=g\int\frac{d^3p}{(2\pi)^3}\left(\frac{1}{e^{(E^*-\mu^*)/T}+1}-\frac{1}{e^{(E^*+\mu^*)/T}+1}\right)
\end{equation}
Using spherical symmetry $d^3p=4\pi dp$ and a variable substitution $dE^*/dp=p/E^*$:
\begin{equation}
    n=g\int_{m^*}^\infty\frac{dE^*}{2\pi^2} E^*\sqrt{(E^*)^2-(m^*)^2}\left(\frac{1}{e^{(E^*-\mu^*)/T}+1}-\frac{1}{e^{(E^*+\mu^*)/T}+1}\right)
\end{equation}
For high temperatures, we can transition to Boltzmann statistics where $e^{(E^*\pm\mu^*)/T}+1\rightarrow e^{(E^*\pm\mu^*)/T}$, the integral becomes 
\begin{equation}
    n=\frac{g}{2\pi^2}(m^*)^2T~K_2\left(\frac{m^*}{T}\right)\sinh\left(\frac{\mu^*}{T}\right)
\end{equation}
where $K$ is a hyperbolic Bessel function of the second type. Notice $K_2(m^*/T)\rightarrow 2(T/m^*)^2$ at high temperature.  Then plugging this into the chemical potential gap equation $\mu^*=\mu-G_\omega n$, we get 
\begin{equation}
    \mu^*=\mu-G_\omega\frac{ gT^3}{\pi^2}\sinh\left(\frac{\mu^*}{T}\right)
\end{equation}
and since we're at the high-temperature limit, we can approximate $\sinh(\mu^*/T)\rightarrow\mu^*/T$, and we can solve for $\mu^*$ as
\begin{equation}
    \mu^*=\frac{\mu}{1+\tilde G_\omega T^2}
\end{equation}
where $\tilde G_\omega=G_\omega \dfrac{g}{\pi^2}$. 
