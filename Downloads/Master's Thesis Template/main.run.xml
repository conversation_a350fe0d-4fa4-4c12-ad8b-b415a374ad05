<?xml version="1.0" standalone="yes"?>
<!-- logreq request file -->
<!-- logreq version 1.0 / dtd version 1.0 -->
<!-- Do not edit this file! -->
<!DOCTYPE requests [
  <!ELEMENT requests (internal | external)*>
  <!ELEMENT internal (generic, (provides | requires)*)>
  <!ELEMENT external (generic, cmdline?, input?, output?, (provides | requires)*)>
  <!ELEMENT cmdline (binary, (option | infile | outfile)*)>
  <!ELEMENT input (file)+>
  <!ELEMENT output (file)+>
  <!ELEMENT provides (file)+>
  <!ELEMENT requires (file)+>
  <!ELEMENT generic (#PCDATA)>
  <!ELEMENT binary (#PCDATA)>
  <!ELEMENT option (#PCDATA)>
  <!ELEMENT infile (#PCDATA)>
  <!ELEMENT outfile (#PCDATA)>
  <!ELEMENT file (#PCDATA)>
  <!ATTLIST requests
    version CDATA #REQUIRED
  >
  <!ATTLIST internal
    package CDATA #REQUIRED
    priority (9) #REQUIRED
    active (0 | 1) #REQUIRED
  >
  <!ATTLIST external
    package CDATA #REQUIRED
    priority (1 | 2 | 3 | 4 | 5 | 6 | 7 | 8) #REQUIRED
    active (0 | 1) #REQUIRED
  >
  <!ATTLIST provides
    type (static | dynamic | editable) #REQUIRED
  >
  <!ATTLIST requires
    type (static | dynamic | editable) #REQUIRED
  >
  <!ATTLIST file
    type CDATA #IMPLIED
  >
]>
<requests version="1.0">
  <internal package="biblatex" priority="9" active="1">
    <generic>latex</generic>
    <provides type="dynamic">
      <file>main.bcf</file>
    </provides>
    <requires type="dynamic">
      <file>main.bbl</file>
    </requires>
    <requires type="static">
      <file>blx-dm.def</file>
      <file>blx-unicode.def</file>
      <file>blx-compat.def</file>
      <file>biblatex.def</file>
      <file>standard.bbx</file>
      <file>numeric.bbx</file>
      <file>numeric-comp.bbx</file>
      <file>ieee.bbx</file>
      <file>numeric-verb.cbx</file>
      <file>ieee.cbx</file>
      <file>biblatex.cfg</file>
      <file>english.lbx</file>
    </requires>
  </internal>
  <external package="biblatex" priority="5" active="1">
    <generic>biber</generic>
    <cmdline>
      <binary>biber</binary>
      <infile>main</infile>
    </cmdline>
    <input>
      <file>main.bcf</file>
    </input>
    <output>
      <file>main.bbl</file>
    </output>
    <provides type="dynamic">
      <file>main.bbl</file>
    </provides>
    <requires type="dynamic">
      <file>main.bcf</file>
    </requires>
    <requires type="editable">
      <file>citations.bib</file>
    </requires>
  </external>
</requests>
