n_s_val = (m_0 - eff_mass_arr[i]) * (m_s/g_s)**2

while(error_a > maxError and error_b > maxError):

    n_s_a = n_s(eff_mass, eff_mu - d_mu, T)
    n_s_b = n_s(eff_mass, eff_mu + d_mu, T)

    error_a = abs(n_s_a - n_s_val) / n_s_val
    error_b = abs(n_s_b - n_s_val) / n_s_val

    if error_a > error_b:
        if prev == 'a':
            d_mu *= 0.5
        prev = 'b'
        eff_mu += d_mu
    else:
        if prev == 'b':
            d_mu *= 0.5
        prev = 'a'
        eff_mu -= d_mu
