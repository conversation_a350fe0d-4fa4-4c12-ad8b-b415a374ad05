\relax 
\providecommand\hyper@newdestlabel[2]{}
\providecommand\HyperFirstAtBeginDocument{\AtBeginDocument}
\HyperFirstAtBeginDocument{\ifx\hyper@anchor\@undefined
\global\let\oldnewlabel\newlabel
\gdef\newlabel#1#2{\newlabelxx{#1}#2}
\gdef\newlabelxx#1#2#3#4#5#6{\oldnewlabel{#1}{{#2}{#3}}}
\AtEndDocument{\ifx\hyper@anchor\@undefined
\let\newlabel\oldnewlabel
\fi}
\fi}
\global\let\hyper@last\relax 
\gdef\HyperFirstAtBeginDocument#1{#1}
\providecommand*\HyPL@Entry[1]{}
\abx@aux@refcontext{none/global//global/global}
\HyPL@Entry{0<</S/r>>}
\HyPL@Entry{1<</S/r>>}
\abx@aux@cite{0}{<PERSON><PERSON><PERSON>_Gale_2023}
\abx@aux@segm{0}{0}{Kapusta_Gale_2023}
\HyPL@Entry{5<</S/D>>}
\@writefile{toc}{\contentsline {section}{\numberline {1}Introduction}{1}{section.1}\protected@file@percent }
\abx@aux@cite{0}{Kapusta_Gale_2023}
\abx@aux@segm{0}{0}{Kapusta_Gale_2023}
\@writefile{toc}{\contentsline {section}{\numberline {2}Hadron Gases}{2}{section.2}\protected@file@percent }
\@writefile{toc}{\contentsline {subsection}{\numberline {2.1}Ideal Fermion Gas}{2}{subsection.2.1}\protected@file@percent }
\newlabel{dirac_lagrange}{{2.1}{2}{Ideal Fermion Gas}{equation.2.1}{}}
\newlabel{partition_func_0}{{2.2}{2}{Ideal Fermion Gas}{equation.2.2}{}}
\abx@aux@cite{0}{grassmann1844}
\abx@aux@segm{0}{0}{grassmann1844}
\abx@aux@cite{0}{Kapusta_Gale_2023}
\abx@aux@segm{0}{0}{Kapusta_Gale_2023}
\newlabel{p_FG_1}{{2.14}{4}{Ideal Fermion Gas}{equation.2.14}{}}
\abx@aux@cite{0}{walecka1974}
\abx@aux@segm{0}{0}{walecka1974}
\@writefile{toc}{\contentsline {subsection}{\numberline {2.2}Walecka Model}{5}{subsection.2.2}\protected@file@percent }
\newlabel{walecka_0}{{2.15}{5}{Walecka Model}{equation.2.15}{}}
\newlabel{eq_motion_0}{{2.18}{6}{Walecka Model}{equation.2.18}{}}
\newlabel{gap_eq}{{2.19}{6}{Walecka Model}{equation.2.19}{}}
\newlabel{Eqs_of_motion}{{2.21}{6}{Walecka Model}{equation.2.21}{}}
\newlabel{partition_func}{{2.22}{7}{Walecka Model}{equation.2.22}{}}
\newlabel{partition_argument_1}{{2.23}{7}{Walecka Model}{equation.2.23}{}}
\newlabel{eff_eqs_1}{{2.24}{7}{Walecka Model}{equation.2.24}{}}
\newlabel{partition_argument}{{2.25}{7}{Walecka Model}{equation.2.25}{}}
\newlabel{presure_w_fields}{{2.30}{8}{Walecka Model}{equation.2.30}{}}
\newlabel{fields_densities_1}{{2.32}{9}{Walecka Model}{equation.2.32}{}}
\newlabel{fields_densities}{{2.33}{9}{Walecka Model}{equation.2.33}{}}
\newlabel{sigma_field}{{2.34}{9}{Walecka Model}{equation.2.34}{}}
\newlabel{omega_field}{{2.35}{9}{Walecka Model}{equation.2.35}{}}
\newlabel{eff_eqs}{{2.37}{10}{Walecka Model}{equation.2.37}{}}
\newlabel{P_eq}{{2.38}{10}{Walecka Model}{equation.2.38}{}}
\newlabel{p_FG}{{2.39}{10}{Walecka Model}{equation.2.39}{}}
\newlabel{vector_density}{{2.40}{10}{Walecka Model}{equation.2.40}{}}
\newlabel{scalar_density}{{2.41}{10}{Walecka Model}{equation.2.41}{}}
\newlabel{Fermi_Dirac_Distribution}{{2.42}{10}{Walecka Model}{equation.2.42}{}}
\@writefile{toc}{\contentsline {subsection}{\numberline {2.3}Zero Temperature Limit}{11}{subsection.2.3}\protected@file@percent }
\newlabel{p_FG_0}{{2.44}{11}{Zero Temperature Limit}{equation.2.44}{}}
\newlabel{P_T_0}{{2.48}{12}{Zero Temperature Limit}{equation.2.48}{}}
\newlabel{n_s_T_0}{{2.51}{12}{Zero Temperature Limit}{equation.2.51}{}}
\newlabel{E_T_0}{{2.52}{12}{Zero Temperature Limit}{equation.2.52}{}}
\providecommand*\caption@xref[2]{\@setref\relax\@undefined{#1}}
\newlabel{fig:sfig1}{{1a}{13}{Energy}{figure.caption.1}{}}
\newlabel{sub@fig:sfig1}{{a}{13}{Energy}{figure.caption.1}{}}
\newlabel{fig:sfig2}{{1b}{13}{Pressure}{figure.caption.1}{}}
\newlabel{sub@fig:sfig2}{{b}{13}{Pressure}{figure.caption.1}{}}
\@writefile{lof}{\contentsline {figure}{\numberline {1}{\ignorespaces Energy Plot}}{13}{figure.caption.1}\protected@file@percent }
\newlabel{zeroTempPlots}{{1}{13}{Energy Plot}{figure.caption.1}{}}
\@writefile{toc}{\contentsline {subsection}{\numberline {2.4}Finite Temperature}{13}{subsection.2.4}\protected@file@percent }
\newlabel{fig:nonlinear1}{{2a}{16}{\relax }{figure.caption.2}{}}
\newlabel{sub@fig:nonlinear1}{{a}{16}{\relax }{figure.caption.2}{}}
\newlabel{fig:nonlinear2}{{2b}{16}{\relax }{figure.caption.2}{}}
\newlabel{sub@fig:nonlinear2}{{b}{16}{\relax }{figure.caption.2}{}}
\@writefile{lof}{\contentsline {figure}{\numberline {2}{\ignorespaces Finite temperature contributions to pressure at $T=10\nobreakspace  {}\text  {MeV}$.}}{16}{figure.caption.2}\protected@file@percent }
\newlabel{fig:P_contributions}{{2}{16}{Finite temperature contributions to pressure at $T=10~\text {MeV}$}{figure.caption.2}{}}
\@writefile{lof}{\contentsline {figure}{\numberline {3}{\ignorespaces Pressure per baryon density at finite temperature numerically calculated in Python. A black dashed line at 0 is added to see the liquid-gas phase transition.}}{17}{figure.caption.3}\protected@file@percent }
\newlabel{fig:P_plot}{{3}{17}{Pressure per baryon density at finite temperature numerically calculated in Python. A black dashed line at 0 is added to see the liquid-gas phase transition}{figure.caption.3}{}}
\@writefile{lof}{\contentsline {figure}{\numberline {4}{\ignorespaces Binding Energy and Entropy Density for finite temperature. }}{17}{figure.caption.4}\protected@file@percent }
\newlabel{fig:binding_energy}{{4}{17}{Binding Energy and Entropy Density for finite temperature}{figure.caption.4}{}}
\@writefile{toc}{\contentsline {subsection}{\numberline {2.5}Entropy Density and Binding Energy for Finite Temperatures}{18}{subsection.2.5}\protected@file@percent }
\newlabel{entropy_0}{{2.55}{18}{Entropy Density and Binding Energy for Finite Temperatures}{equation.2.55}{}}
\newlabel{e_I}{{2.58}{18}{Entropy Density and Binding Energy for Finite Temperatures}{equation.2.58}{}}
\@writefile{toc}{\contentsline {subsection}{\numberline {2.6}Liquid-Gas Phase Transition}{19}{subsection.2.6}\protected@file@percent }
\newlabel{Liquid_gas_phase}{{2.6}{19}{Liquid-Gas Phase Transition}{subsection.2.6}{}}
\abx@aux@cite{0}{Kapusta_Gale_2023}
\abx@aux@segm{0}{0}{Kapusta_Gale_2023}
\@writefile{lof}{\contentsline {figure}{\numberline {5}{\ignorespaces Pressure and binding energy as functions of chemical potential. These quantities are multivalued before the phase transition ($T<T_c\approx 15\nobreakspace  {}\text  {MeV}$) and single-valued after the transition.}}{20}{figure.caption.5}\protected@file@percent }
\newlabel{fig:phase_transitions}{{5}{20}{Pressure and binding energy as functions of chemical potential. These quantities are multivalued before the phase transition ($T<T_c\approx 15~\text {MeV}$) and single-valued after the transition}{figure.caption.5}{}}
\@writefile{toc}{\contentsline {subsection}{\numberline {2.7}Multiple Body interactions}{20}{subsection.2.7}\protected@file@percent }
\newlabel{multi_body_L}{{2.59}{20}{Multiple Body interactions}{equation.2.59}{}}
\newlabel{multi_body_density}{{2.60}{20}{Multiple Body interactions}{equation.2.60}{}}
\@writefile{lof}{\contentsline {figure}{\numberline {6}{\ignorespaces Third-body interactions at nonlinear parameter $b=7.95\times 10^{-3}$ and coupling constants $g^2_\sigma /4\pi =6.003$ and $g_\omega ^2/4\pi =5.948$.}}{21}{figure.caption.6}\protected@file@percent }
\newlabel{fig:E_nonlinear_plot}{{6}{21}{Third-body interactions at nonlinear parameter $b=7.95\times 10^{-3}$ and coupling constants $g^2_\sigma /4\pi =6.003$ and $g_\omega ^2/4\pi =5.948$}{figure.caption.6}{}}
\newlabel{P_eq_nonlinear}{{2.61}{21}{Multiple Body interactions}{equation.2.61}{}}
\abx@aux@cite{0}{Klaehn}
\abx@aux@segm{0}{0}{Klaehn}
\@writefile{toc}{\contentsline {section}{\numberline {3}Density Functional Approach for Isospin symmetric nuclear matter}{22}{section.3}\protected@file@percent }
\newlabel{pressure_split}{{3.4}{23}{Density Functional Approach for Isospin symmetric nuclear matter}{equation.3.4}{}}
\newlabel{gap_0}{{3.5}{23}{Density Functional Approach for Isospin symmetric nuclear matter}{equation.3.5}{}}
\newlabel{densities_definition}{{3.6}{23}{Density Functional Approach for Isospin symmetric nuclear matter}{equation.3.6}{}}
\newlabel{Legendre_0}{{3.8}{24}{Density Functional Approach for Isospin symmetric nuclear matter}{equation.3.8}{}}
\newlabel{partial_fields}{{3.9}{24}{Density Functional Approach for Isospin symmetric nuclear matter}{equation.3.9}{}}
\newlabel{gap_1}{{3.10}{24}{Density Functional Approach for Isospin symmetric nuclear matter}{equation.3.10}{}}
\newlabel{Legendre_1}{{3.11}{24}{Density Functional Approach for Isospin symmetric nuclear matter}{equation.3.11}{}}
\newlabel{pressure_split_0}{{3.12}{24}{Density Functional Approach for Isospin symmetric nuclear matter}{equation.3.12}{}}
\newlabel{pressure_split_formalism}{{3.13}{25}{Density Functional Approach for Isospin symmetric nuclear matter}{equation.3.13}{}}
\newlabel{p_FG_formalism}{{3.15}{25}{Density Functional Approach for Isospin symmetric nuclear matter}{equation.3.15}{}}
\newlabel{densities_integral}{{3.16}{25}{Density Functional Approach for Isospin symmetric nuclear matter}{equation.3.16}{}}
\newlabel{gap_formalism}{{3.17}{26}{Density Functional Approach for Isospin symmetric nuclear matter}{equation.3.17}{}}
\@writefile{toc}{\contentsline {subsection}{\numberline {3.1}Recreating Walecka Model}{26}{subsection.3.1}\protected@file@percent }
\@writefile{toc}{\contentsline {subsubsection}{\numberline {3.1.1}Generalized Densities of Vector Interactions}{27}{subsubsection.3.1.1}\protected@file@percent }
\@writefile{lof}{\contentsline {figure}{\numberline {7}{\ignorespaces Pressure using no scalar field to test the density functional approach using a relaxation method with parameter $\gamma =0.05$ and ranging $\nu $ to the values $1200-3000\nobreakspace  {}\text  {MeV}$}}{29}{figure.caption.7}\protected@file@percent }
\newlabel{fig:zeroTempNoMass}{{7}{29}{Pressure using no scalar field to test the density functional approach using a relaxation method with parameter $\gamma =0.05$ and ranging $\nu $ to the values $1200-3000~\text {MeV}$}{figure.caption.7}{}}
\newlabel{effective_potentials}{{3.25}{29}{Generalized Densities of Vector Interactions}{equation.3.25}{}}
\@writefile{lof}{\contentsline {figure}{\numberline {8}{\ignorespaces Pressure to test the density functional approach using a relaxation method with parameters $\gamma =0.05$ and $\delta =0.005$, ranging $\nu $ to the values $1300-1700\nobreakspace  {}\text  {MeV}${\color  {red}Lower axis might not be scaled correctly, might have to redo}}}{31}{figure.caption.8}\protected@file@percent }
\newlabel{fig:ZeroTemp}{{8}{31}{Pressure to test the density functional approach using a relaxation method with parameters $\gamma =0.05$ and $\delta =0.005$, ranging $\nu $ to the values $1300-1700~\text {MeV}${\color {red}Lower axis might not be scaled correctly, might have to redo}}{figure.caption.8}{}}
\@writefile{toc}{\contentsline {subsubsection}{\numberline {3.1.2}Adding a Scalar interaction}{31}{subsubsection.3.1.2}\protected@file@percent }
\@writefile{toc}{\contentsline {subsubsection}{\numberline {3.1.3}Finite-Temperature Limit}{32}{subsubsection.3.1.3}\protected@file@percent }
\newlabel{fig:mass_chemicalPotentials}{{9a}{33}{\relax }{figure.caption.9}{}}
\newlabel{sub@fig:mass_chemicalPotentials}{{a}{33}{\relax }{figure.caption.9}{}}
\newlabel{fig:mass_chemicalPotentials_Zoom}{{9b}{33}{\relax }{figure.caption.9}{}}
\newlabel{sub@fig:mass_chemicalPotentials_Zoom}{{b}{33}{\relax }{figure.caption.9}{}}
\@writefile{lof}{\contentsline {figure}{\numberline {9}{\ignorespaces Effective mass, effective chemical potential, and chemical potential as a function of baryon density.}}{33}{figure.caption.9}\protected@file@percent }
\newlabel{fig:chem_potential}{{9}{33}{Effective mass, effective chemical potential, and chemical potential as a function of baryon density}{figure.caption.9}{}}
\@writefile{lof}{\contentsline {figure}{\numberline {10}{\ignorespaces Effective masses, effective chemical potentials, and chemical potential as a function of baryon density for different shifts between the multiplicity and baryon coupling constants. This shows that the values are very different.}}{34}{figure.caption.10}\protected@file@percent }
\newlabel{fig:gen_chem_potential}{{10}{34}{Effective masses, effective chemical potentials, and chemical potential as a function of baryon density for different shifts between the multiplicity and baryon coupling constants. This shows that the values are very different}{figure.caption.10}{}}
\@writefile{lof}{\contentsline {figure}{\numberline {11}{\ignorespaces Liquid-gas phase transition as seen by how the pressure changes with baryon density and chemical potential. Notice how the parameters $\alpha $ and $\beta $ do not change the result as pressure is thermodynamically consistent in the multiplicity picture with chemical potential.}}{34}{figure.caption.11}\protected@file@percent }
\newlabel{fig:pressure_phase}{{11}{34}{Liquid-gas phase transition as seen by how the pressure changes with baryon density and chemical potential. Notice how the parameters $\alpha $ and $\beta $ do not change the result as pressure is thermodynamically consistent in the multiplicity picture with chemical potential}{figure.caption.11}{}}
\abx@aux@cite{0}{excludedVolume}
\abx@aux@segm{0}{0}{excludedVolume}
\@writefile{lof}{\contentsline {figure}{\numberline {12}{\ignorespaces Antiparticles may appear at zero temp! {\color  {red}Rewrite}}}{35}{figure.caption.12}\protected@file@percent }
\newlabel{fig:antiparticlesZeroTemp}{{12}{35}{Antiparticles may appear at zero temp! {\color {red}Rewrite}}{figure.caption.12}{}}
\@writefile{lof}{\contentsline {figure}{\numberline {13}{\ignorespaces Excluded volume using $r=0.5\nobreakspace  {}\text  {fm}$.}}{35}{figure.caption.13}\protected@file@percent }
\newlabel{fig:excludedvolumeFig}{{13}{35}{Excluded volume using $r=0.5~\text {fm}$}{figure.caption.13}{}}
\@writefile{toc}{\contentsline {subsubsection}{\numberline {3.1.4}Quantities under Formalism}{35}{subsubsection.3.1.4}\protected@file@percent }
\@writefile{toc}{\contentsline {subsection}{\numberline {3.2}Comparison to Excluded Volume Approach}{35}{subsection.3.2}\protected@file@percent }
\@writefile{toc}{\contentsline {subsection}{\numberline {3.3}Isospin Asymmetric Nuclear Matter}{36}{subsection.3.3}\protected@file@percent }
\abx@aux@cite{0}{Klaehn}
\abx@aux@segm{0}{0}{Klaehn}
\@writefile{toc}{\contentsline {section}{\numberline {4}Speed of Sound}{39}{section.4}\protected@file@percent }
\@writefile{toc}{\contentsline {subsection}{\numberline {4.1}High-Temperature limit}{40}{subsection.4.1}\protected@file@percent }
\@writefile{toc}{\contentsline {subsection}{\numberline {4.2}High-density limit}{40}{subsection.4.2}\protected@file@percent }
\@writefile{toc}{\contentsline {section}{Appendix}{41}{equation.4.9}\protected@file@percent }
\@writefile{lof}{\contentsline {figure}{\numberline {14}{\ignorespaces Pressure contribution of scalar and vector field as a function of temperature density for constant chemical potential.}}{43}{figure.caption.14}\protected@file@percent }
\newlabel{fig:field_contributions}{{14}{43}{Pressure contribution of scalar and vector field as a function of temperature density for constant chemical potential}{figure.caption.14}{}}
\newlabel{mass_gap}{{A.23}{44}{}{equation.Alph0.23}{}}
\abx@aux@nociteall
\gdef\minted@oldcachelist{,
  default.pygstyle,
  D00A69C496A38420FE2E5616D2854EB1541C68F80C6A6F06151AFD63BAE50EBC.pygtex,
  AA544220C54527F7D9864444C50EC722541C68F80C6A6F06151AFD63BAE50EBC.pygtex}
\abx@aux@read@bbl@mdfivesum{nohash}
\abx@aux@read@bblrerun
\abx@aux@defaultrefcontext{0}{Kapusta_Gale_2023}{none/global//global/global}
\abx@aux@defaultrefcontext{0}{grassmann1844}{none/global//global/global}
\abx@aux@defaultrefcontext{0}{walecka1974}{none/global//global/global}
\abx@aux@defaultrefcontext{0}{Klaehn}{none/global//global/global}
\abx@aux@defaultrefcontext{0}{excludedVolume}{none/global//global/global}
\gdef \@abspage@last{53}
