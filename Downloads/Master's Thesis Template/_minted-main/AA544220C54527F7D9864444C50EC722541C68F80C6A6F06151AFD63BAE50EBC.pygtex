\begin{Verbatim}[commandchars=\\\{\}]
\PYG{n}{n\PYGZus{}s\PYGZus{}val} \PYG{o}{=} \PYG{p}{(}\PYG{n}{m\PYGZus{}0} \PYG{o}{\PYGZhy{}} \PYG{n}{eff\PYGZus{}mass\PYGZus{}arr}\PYG{p}{[}\PYG{n}{i}\PYG{p}{])} \PYG{o}{*} \PYG{p}{(}\PYG{n}{m\PYGZus{}s}\PYG{o}{/}\PYG{n}{g\PYGZus{}s}\PYG{p}{)}\PYG{o}{**}\PYG{l+m+mi}{2}

\PYG{k}{while}\PYG{p}{(}\PYG{n}{error\PYGZus{}a} \PYG{o}{\PYGZgt{}} \PYG{n}{maxError} \PYG{o+ow}{and} \PYG{n}{error\PYGZus{}b} \PYG{o}{\PYGZgt{}} \PYG{n}{maxError}\PYG{p}{):}

    \PYG{n}{n\PYGZus{}s\PYGZus{}a} \PYG{o}{=} \PYG{n}{n\PYGZus{}s}\PYG{p}{(}\PYG{n}{eff\PYGZus{}mass}\PYG{p}{,} \PYG{n}{eff\PYGZus{}mu} \PYG{o}{\PYGZhy{}} \PYG{n}{d\PYGZus{}mu}\PYG{p}{,} \PYG{n}{T}\PYG{p}{)}
    \PYG{n}{n\PYGZus{}s\PYGZus{}b} \PYG{o}{=} \PYG{n}{n\PYGZus{}s}\PYG{p}{(}\PYG{n}{eff\PYGZus{}mass}\PYG{p}{,} \PYG{n}{eff\PYGZus{}mu} \PYG{o}{+} \PYG{n}{d\PYGZus{}mu}\PYG{p}{,} \PYG{n}{T}\PYG{p}{)}

    \PYG{n}{error\PYGZus{}a} \PYG{o}{=} \PYG{n+nb}{abs}\PYG{p}{(}\PYG{n}{n\PYGZus{}s\PYGZus{}a} \PYG{o}{\PYGZhy{}} \PYG{n}{n\PYGZus{}s\PYGZus{}val}\PYG{p}{)} \PYG{o}{/} \PYG{n}{n\PYGZus{}s\PYGZus{}val}
    \PYG{n}{error\PYGZus{}b} \PYG{o}{=} \PYG{n+nb}{abs}\PYG{p}{(}\PYG{n}{n\PYGZus{}s\PYGZus{}b} \PYG{o}{\PYGZhy{}} \PYG{n}{n\PYGZus{}s\PYGZus{}val}\PYG{p}{)} \PYG{o}{/} \PYG{n}{n\PYGZus{}s\PYGZus{}val}

    \PYG{k}{if} \PYG{n}{error\PYGZus{}a} \PYG{o}{\PYGZgt{}} \PYG{n}{error\PYGZus{}b}\PYG{p}{:}
        \PYG{k}{if} \PYG{n}{prev} \PYG{o}{==} \PYG{l+s+s1}{\PYGZsq{}a\PYGZsq{}}\PYG{p}{:}
            \PYG{n}{d\PYGZus{}mu} \PYG{o}{*=} \PYG{l+m+mf}{0.5}
        \PYG{n}{prev} \PYG{o}{=} \PYG{l+s+s1}{\PYGZsq{}b\PYGZsq{}}
        \PYG{n}{eff\PYGZus{}mu} \PYG{o}{+=} \PYG{n}{d\PYGZus{}mu}
    \PYG{k}{else}\PYG{p}{:}
        \PYG{k}{if} \PYG{n}{prev} \PYG{o}{==} \PYG{l+s+s1}{\PYGZsq{}b\PYGZsq{}}\PYG{p}{:}
            \PYG{n}{d\PYGZus{}mu} \PYG{o}{*=} \PYG{l+m+mf}{0.5}
        \PYG{n}{prev} \PYG{o}{=} \PYG{l+s+s1}{\PYGZsq{}a\PYGZsq{}}
        \PYG{n}{eff\PYGZus{}mu} \PYG{o}{\PYGZhy{}=} \PYG{n}{d\PYGZus{}mu}
\end{Verbatim}
